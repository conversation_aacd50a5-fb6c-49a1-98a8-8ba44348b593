<template>
  <div>
    <a-modal v-model:open="dlgOpen" dialog-class="pwd-form-dlg-container" title="算力豆获取" @ok="handleOk" @cancel="closeModal">
      <a-form ref="ruleForm" :colon="false" :model="form" :rules="rules">
        <a-form-item ref="code" label="邀请码验证" name="code">
          <a-input v-model:value="form.code" placeholder="请输入邀请码" />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal v-model:open="resultVisible" dialog-class="pwd-form-dlg-container" title="算力豆获取" @ok="resultVisible = false">
      <a-result style="padding: 0" status="success" title="" :sub-title="`验证成功，您获得${result.computeCount}算力豆，当前可用算力豆为${result.availableCount}`"></a-result>
      <template #footer>
        <a-button key="submit" type="primary" @click="resultVisible = false"> 确定 </a-button>
      </template>
    </a-modal>
  </div>
</template>
<script>
import { POST } from '@/request';

export default {
  props: {
    dlgVisible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['closeInvitationCodeModal', 'getDetail'],
  data() {
    return {
      dlgOpen: false,
      form: { code: '' },
      errorMessage: '',
      resultVisible: false,
      result: {},
      status: 'origin',
    };
  },
  computed: {
    rules() {
      return {
        code: [
          { required: true, message: '请输入邀请码', trigger: 'blur' },
          { validator: this.codeValidator, trigger: 'blur' },
        ],
      };
    },
  },
  watch: {
    dlgVisible(val) {
      this.dlgOpen = val;
      this.$refs['ruleForm'] && this.$refs['ruleForm'].resetFields();
    },
  },
  created() {
    this.dlgOpen = this.dlgVisible;
  },
  methods: {
    handleOk() {
      this.$refs['ruleForm']
        .validate()
        .then((valid) => {
          if (valid) {
            return POST(`/accounting/web/invitation/exchange-code`, { code: this.form.code });
          } else {
            return Promise.reject(new Error('表单验证失败'));
          }
        })
        .then((res) => {
          if (res.errorCode) {
            this.errorMessage = res.errorMessage;
            this.$refs['ruleForm'].validateField('code');
            return Promise.reject(new Error(res.errorMessage));
          } else {
            this.closeModal();
            this.getDetail();
            this.result = res.body;
            this.resultVisible = true;
          }
        })
        .catch((error) => {
          console.error('验证或请求失败:');
        });
    },
    closeModal() {
      this.$emit('closeInvitationCodeModal');
    },
    getDetail() {
      this.$emit('getDetail');
    },
    codeValidator(rule, value, callback) {
      if (this.errorMessage) {
        callback(new Error(this.errorMessage));
        this.errorMessage = '';
      } else {
        callback();
      }
    },
  },
};
</script>
