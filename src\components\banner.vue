<template>
  <div class="dataCenter">
    <div class="window" @mouseover="stop" @mouseleave="play">
      <ul class="container" :style="containerStyle">
        <div class="swiper-slides item" :style="{ width: imgWidth + 'px' }">
          <div class="inner" style="display: flex; justify-content: space-between; align-items: center; overflow: hidden; width: 1200px; margin: 0 auto">
            <div class="text">
              <p v-if="sliders[sliders.length - 1].h1 !== ''">
                {{ sliders[sliders.length - 1].h1 }}
              </p>
              <span v-if="sliders[sliders.length - 1].h2 !== ''">{{ sliders[sliders.length - 1].h2 }}</span>
              <strong v-if="sliders[sliders.length - 1].p2">
                <p>{{ sliders[sliders.length - 1].p }}</p>
                <p>{{ sliders[sliders.length - 1].p2 }}</p>
              </strong>
              <strong v-else>{{ sliders[sliders.length - 1].p }}</strong>

              <slot v-if="sliders[sliders.length - 1].btn != '即将开始'">
                <button v-if="sliders[sliders.length - 1].btn != ''" class="liJi" @click="toDetail(sliders[sliders.length - 1])">
                  {{ sliders[sliders.length - 1].btn }}
                </button>
              </slot>
              <slot v-else>
                <jt-tooltip>
                  <template #title>敬请期待</template>
                  <button v-if="sliders[sliders.length - 1].btn != ''" class="liJi" @click="toDetail(sliders[sliders.length - 1])">
                    {{ sliders[sliders.length - 1].btn }}
                  </button>
                </jt-tooltip>
              </slot>
            </div>
            <div style="width: 460px; height: 216px">
              <img :src="sliders[sliders.length - 1].img" class="img" alt />
            </div>
          </div>
        </div>

        <div v-for="(item, index) in sliders" :key="index" class="swiper-slides item" :style="{ width: imgWidth + 'px' }">
          <div class="inner" style="display: flex; justify-content: space-between; align-items: center; width: 1200px; margin: 0 auto">
            <div class="text" :class="item.id == 4 ? 'gain-suanli-text' : ''">
              <p v-if="item.h1 !== ''">{{ item.h1 }}</p>
              <span v-if="item.h2 !== ''">{{ item.h2 }}</span>
              <strong v-if="item.p2">
                <p>{{ item.p }}</p>
                <p>{{ item.p2 }}</p>
              </strong>
              <strong v-else>{{ item.p }}</strong>

              <slot v-if="item.btn != '即将开始'">
                <button v-if="item.btn != '' && !item.disabled" class="liJi" @click="toDetail(item)">
                  {{ item.btn }}
                </button>
              </slot>
              <slot v-else>
                <jt-tooltip>
                  <template #title>敬请期待</template>
                  <button v-if="item.btn != ''" class="liJi" @click="toDetail(item)">
                    {{ item.btn }}
                  </button>
                </jt-tooltip>
              </slot>
            </div>
            <div style="width: 460px; height: 216px" :class="item.id == 4 ? 'gain-suanli' : item.id == 5 ? 'automation-modeling' : ''">
              <img :src="item.img" class="img" alt />
            </div>
          </div>
        </div>

        >

        <div class="swiper-slides item" :style="{ width: imgWidth + 'px' }">
          <div class="inner" style="display: flex; justify-content: space-between; align-items: center; overflow: hidden; width: 1200px; margin: 0 auto">
            <div class="text">
              <p v-if="sliders[0].h1 !== ''">
                {{ sliders[0].h1 }}
              </p>
              <span v-if="sliders[0].h2 !== ''">{{ sliders[0].h2 }}</span>
              <strong v-if="sliders[0].p2">
                <p>{{ sliders[0].p }}</p>
                <p>{{ sliders[0].p2 }}</p>
              </strong>
              <strong v-else>{{ sliders[0].p }}</strong>

              <slot v-if="sliders[0].btn != '即将开始'">
                <button v-if="sliders[0].btn != ''" class="liJi" @click="toDetail(sliders[0])">
                  {{ sliders[0].btn }}
                </button>
              </slot>
              <slot v-else>
                <jt-tooltip>
                  <template #title>敬请期待</template>
                  <button v-if="sliders[0].btn != ''" class="liJi" @click="toDetail(sliders[0])">
                    {{ sliders[0].btn }}
                  </button>
                </jt-tooltip>
              </slot>
            </div>
            <div style="width: 460px; height: 216px">
              <img class="img" :src="sliders[0].img" alt />
            </div>
          </div>
        </div>
      </ul>

      <!-- 小圆点 -->
      <ul v-if="sliders.length > 1" class="dots">
        <div style="overflow: hidden; width: 1200px; margin: 0 auto">
          <li v-for="(dot, i) in sliders" :key="i" :class="{ dotted: i === currentIndex - 1 }" @click="jump(i + 1)"></li>
          <div class="process">
            <div :style="{ width: health + '%', left: `${linePro}px` }"></div>
          </div>
        </div>
      </ul>
    </div>
  </div>
</template>
<script>
import { Tooltip as JtTooltip } from 'ant-design-vue';
import { getLocalConfig } from '@/config';
const autox_url = ' https://jiutian.10086.cn/autox';
export default {
  name: 'DataCenter',
  components: {
    JtTooltip,
  },
  props: {
    initialSpeed: {
      type: Number,
      default: 30,
    }, // 图片移动速度
    initialInterval: {
      type: Number,
      default: 1,
    }, // 如果是一个组件 接受外部传入的切换周期
    sliders: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      sliderss: [
        //banner图
        {
          id: '0',
          h1: '九天 · 毕昇',
          h2: '一站式人工智能学习和实战平台',
          p: '基于中移九天深度学习平台，为AI学习者提供充沛的GPU算力、丰富的数据和学习实战资源，服务课程学习、比赛打榜、工作求职等全流程场景，并面向高校提供在线教学、科研开发的一站式解决方案',
          btn: '',
          img: require('../assets/image/home/<USER>'),
        },
        {
          id: '1',
          h1: '',
          h2: '人工智能学习课程',
          p: '联手名师名校，打造AI精品课程 ',
          p2: '理论实战结合，带你打开AI世界的大门',
          btn: '立即查看',

          img: require('../assets/image/home/<USER>'),
        },
        {
          id: '2',
          h1: '不服来战',
          h2: '汇集国内外顶尖AI大赛',
          p: '挑战性难题、同场竞技、一较高下',
          btn: '立即查看',

          img: require('../assets/image/home/<USER>'),
        },
        {
          id: '3',
          h1: '',
          h2: 'AI一站式开发环境',
          p: '提供在线开发和模型调试工具，集成主流人工智能开源算法框架，为模型训练提供一站式服务',
          btn: '立即使用',
          img: require('../assets/image/home/<USER>'),
        },
      ],
      windowWidth: document.documentElement.clientWidth, //实时屏幕宽度
      windowHeight: document.documentElement.clientHeight, //实时屏幕高度
      imgWidth: 0, // 图片宽度
      currentIndex: 1, // 原点起始位置
      distance: -0, // 外层嵌套的初始移动距离
      transitionEnd: true, // 防止多次快速点击切换出现问题的闸门
      speed: this.initialSpeed,
      timer: null, // 定时器
      maskBol: false,
      health: 0,
      eable: false,
      timers: null,
      count: 5,
      isPause: false,
      linePro: 30, //进度条当前的位置
    };
  },
  computed: {
    containerStyle() {
      return {
        transform: `translate3d(${this.distance}px, 0, 0)`,
      };
    },
    interval() {
      return this.initialInterval * 5000;
    },
  },
  created() {
    this.init();
    this.timers = setInterval(this.sub, 10);
  },
  mounted() {
    let that = this;
    window.fullHeight = document.documentElement.clientHeight;
    window.fullWidth = document.documentElement.clientWidth;
    that.windowHeight = window.fullHeight; // 高
    that.windowWidth = window.fullWidth; // 宽
    that.imgWidth = that.windowWidth;
    that.distance = -that.windowWidth;

    this.$nextTick(() => {
      window.addEventListener('resize', () => {
        window.fullHeight = document.documentElement.clientHeight;
        window.fullWidth = document.documentElement.clientWidth;
        that.windowHeight = window.fullHeight; // 高
        that.windowWidth = window.fullWidth; // 宽

        that.imgWidth = that.windowWidth;
        that.distance = -that.windowWidth * this.currentIndex;
      });
    });
  },
  beforeUnmount() {
    clearInterval(this.timers);
  },
  methods: {
    toDetail(item) {
      switch (item.id) {
        case '1':
          this.$router.push({
            path: item.route || '/course',
          });
          break;
        case '2':
          this.$router.push({
            path: '/competition',
          });
          break;
        case 'competition-0':
          // this.$router.push({
          //   path: '/competition/competition-detail',
          //   query: {
          //     id: 7,
          //     flag: 2
          //   }
          // })
          break;
        case 'competition-1':
          this.$router.push({
            path: '/competition/competition-detail',
            query: {
              id: this.sliders[0].Peoplenum.cid,
              flag: this.sliders[0].Peoplenum.flag,
              all: 0, // 是否为全部比赛tab进入详情
            },
          });
          break;
        case '4':
          // this.$router.push({
          //   path: '/user-center?activeTab=2',
          // });
          break;
        case '5':
          window.open(autox_url);
          break;
        default:
          window.open(`${getLocalConfig('CONSOLE_URL')}/home/<USER>
          break;
      }
    },
    sub() {
      if (this.isPause == false) {
        this.health += 1 / this.count;

        if (this.health >= 100) {
          clearInterval(this.timers);
          this.move(this.imgWidth, -1, this.speed);
          this.health = 0;
        }
      }
    },

    init() {
      this.plays();
    },
    move(offset, direction, speed) {
      // 移动一次的距离， 向左还是向右移动， 图片移动速度
      if (this.sliders.length <= 1) return;
      if (!this.transitionEnd) return;
      this.transitionEnd = false;
      direction === -1 ? (this.currentIndex += offset / this.imgWidth) : (this.currentIndex -= offset / this.imgWidth);
      if (this.currentIndex > this.sliders.length) this.currentIndex = 1;
      if (this.currentIndex < 1) this.currentIndex = this.sliders.length;

      const destination = this.distance + offset * direction;
      this.animate(destination, direction, speed);
      clearInterval(this.timers);
      this.health = 0;
      this.timers = setInterval(this.sub, 10);

      if (this.currentIndex - 1 == 0) {
        this.linePro = 30;
      } else if (this.currentIndex - 1 == 1) {
        this.linePro = 70;
      } else if (this.currentIndex - 1 == 2) {
        this.linePro = 110;
      } else if (this.currentIndex - 1 == 3) {
        this.linePro = 150;
      } else if (this.currentIndex - 1 == 4) {
        this.linePro = 190;
      } else if (this.currentIndex - 1 == 5) {
        this.linePro = 230;
      }
    },
    animate(des, direc, speed) {
      // 实际移动距离 想左还是向右 移动速度 负右正左
      if (this.temp) {
        window.clearInterval(this.temp);
        this.temp = null;
      }
      this.temp = window.setInterval(() => {
        if ((direc === -1 && des < this.distance) || (direc === 1 && des > this.distance)) {
          this.distance += speed * direc;
        } else {
          this.transitionEnd = true;
          window.clearInterval(this.temp);
          this.distance = des;
          let allWidth = this.sliders.length * this.imgWidth;
          if (des < -allWidth) this.distance = -this.imgWidth;
          if (des > -this.imgWidth) this.distance = -allWidth;
        }
      }, 10);
    },
    jump(index) {
      const direction = index - this.currentIndex >= 0 ? -1 : 1;
      const offset = Math.abs(index - this.currentIndex) * this.imgWidth;
      const jumpSpeed = Math.abs(index - this.currentIndex) === 0 ? this.speed : Math.abs(index - this.currentIndex) * this.speed;
      this.move(offset, direction, jumpSpeed);
    },
    // 已进入页面就开始 动
    plays() {
      if (this.sliders.length > 1) {
        if (!this.maskBol) {
          this.isPause = false;
          if (this.timer) {
            window.clearInterval(this.timer);
            this.timer = null;
          }
          this.timer = window.setInterval(() => {
            this.move(this.imgWidth, -1, this.speed);
          }, this.interval);
        }
      }
    },
    // 自动播放函数
    play() {
      if (this.sliders.length > 1) {
        this.isPause = false;
        if (!this.maskBol) {
          if (this.timer) {
            window.clearInterval(this.timer);
            this.timer = null;
          }
          if (this.timers) {
            clearInterval(this.timers);
            this.timers = null;
          }
          this.timers = setInterval(this.sub, 10);
        }
      }
    },
    stop() {
      this.isPause = true;
      window.clearInterval(this.timer);
      this.timer = null;
      clearInterval(this.timers);
    },
  },
};
</script>
<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.dataCenter {
  // text-align: center;
  height: 460px;
  .window {
    position: relative;
    // width: 1200px;
    height: 460px;
    // margin: 0 auto;
    overflow: hidden;
    .container {
      display: flex;
      position: absolute;
    }
    .left,
    .right {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 50px;
      height: 50px;
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 50%;
      cursor: pointer;
    }
    .left {
      left: 3%;
      padding-left: 12px;
      padding-top: 10px;
    }
    .right {
      right: 3%;
      padding-right: 12px;
      padding-top: 10px;
    }
    img {
      user-select: none;
    }
  }
  .dots {
    position: absolute;
    bottom: 32px;
    width: 100%;
  }
  .dots li {
    display: inline-block;
    width: 32px;
    height: 4px;
    margin-right: 8px;
    background-color: #fff;
    cursor: pointer;
  }
  .mask-div {
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.7);
    position: fixed;
    top: 0;
    left: 0;
    .mask-img {
      margin-top: calc(50vh - 200px);
    }
  }
}

.process {
  width: 32px;
  height: 4px;
  margin-right: 8px;
  position: absolute;
  top: 12px;
  margin-left: -30px;
}

.process div {
  height: 4px;
  width: 32px;
  position: absolute;
  background: #0082ff;
}

.swiper-slides {
  width: 100%;
  height: 460px;
  padding: 43px 0px 61px 0px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 80px;
  overflow: hidden;
  &:nth-of-type(1) {
    background: #d1efff;
  }
  &:nth-of-type(2) {
    background: rgba(0, 130, 255, 0.18);
  }
  &:nth-of-type(3) {
    background: #d1efff;
  }
  &:nth-of-type(4) {
    background: #d6e7ff;
  }
  &:nth-of-type(5) {
    background: #d1e8ff;
  }
  &:nth-of-type(6) {
    background: rgba(0, 130, 255, 0.18);
  }
  &:nth-of-type(7) {
    background: rgba(0, 130, 255, 0.18);
  }

  .img,
  img {
    width: 460px;
    height: 216px;
  }

  .text {
    > p {
      height: 67px;
      font-size: 48px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      line-height: 67px;
    }
    > span {
      font-size: 48px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      line-height: 67px;
      display: inline-block;
    }
    > strong {
      width: 626px;
      font-size: 16px;
      font-weight: @jt-font-weight-medium;
      color: #121f2c;
      line-height: 26px;
      margin-top: 16px;
      margin-bottom: 32px;
      display: inline-block;

      &:nth-of-type(2) {
        color: red;
        margin-top: 0px;
        margin-bottom: 0px;
      }
    }
  }
}

// banner 的立即查看
.liJi {
  width: 128px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background: #0082ff;
  border-radius: 2px;
  color: #ffffff;
  cursor: pointer;
  display: block;
  border: none;
  outline: none;
  z-index: 2;
}
</style>

<style scoped lang="less">
.slide {
  width: 1024px;
  height: 320px;
  margin: 0 auto;
  margin-top: 50px;
  overflow: hidden;
  position: relative;
}
.slideshow {
  width: 1024px;
  height: 320px;
}

.bar {
  position: absolute;
  width: 100%;
  bottom: 10px;
  margin: 0 auto;
  z-index: 10;
  text-align: center;
}
.bar span {
  width: 20px;
  height: 5px;
  border: 1px solid;
  background: white;
  display: inline-block;
  margin-right: 10px;
}
.active {
  background: red !important;
}
.image-enter-active {
  transform: translateX(0);
  transition: all 1.5s ease;
}
.image-leave-active {
  transform: translateX(-100%);
  transition: all 1.5s ease;
}
.image-enter {
  transform: translateX(100%);
}
.image-leave {
  transform: translateX(0);
}
// 算力豆图比其他的banner图大
.gain-suanli-text {
  display: flex;
  flex-direction: column;
}
.gain-suanli {
  // position: relative;
  width: 1069px !important;
  height: 405px !important;
  position: absolute;
  img {
    width: 1069px !important;
    height: 405px !important;
    position: absolute;
    left: 168px;
    object-fit: contain;
  }
}
.automation-modeling {
  width: 503px !important;
  height: 380px !important;
  position: relative;
  top: 30px;
  right: 0px;
  img {
    width: 503px !important;
    height: 380px !important;
  }
}
.not-allowed {
  cursor: not-allowed;
}
</style>
