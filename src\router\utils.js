import { Modal } from 'ant-design-vue';

export const windowBeforeUnloadListener = (e) => {
  const msg = '这个提示语不会被显示';
  const event = e || window.event;

  // 兼容IE8和Firefox 4之前的版本
  if (event) {
    event.preventDefault();
    event.returnValue = msg;
  }

  // Chrome, Safari, Firefox 4+, Opera 12+ , IE 9+
  return msg;
};

export const updateBeforeUnloadListener = (to, from) => {
  // query变化也会触发，所以需要增加路径相同的逻辑处理
  if (to.path === from.path) return;
  console.log(to, 'to');

  console.log(to.meta.confirmLeave ? '绑定 beforeunload' : '移除 beforeunload');
  window.onbeforeunload = to.meta.confirmLeave ? windowBeforeUnloadListener : null;
};

export const confirmBeforeLeavingPage = (to, from, next) => {
  // 处理路径相同或者不需要弹窗的情况
  if (to.path === from.path || !from.meta.confirmLeave || to.query.noLeaveConfirm) {
    return true;
  }

  Modal.confirm({
    title: `确定离开当前页面吗？`,
    content: `编辑内容将丢失，请谨慎操作`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      next();
    },
    onCancel: () => {
      window.onbeforeunload = windowBeforeUnloadListener;
    },
  });

  return false;
};
