<template>
  <div>
    <div class="edit-header">{{ isPreview ? '预览' : '编辑' }}报名及团队设置</div>
    <div v-show="!isPreview" class="main">
      <a-form ref="editForm" :model="formData" :colon="false" @submit="handleSubmit">
        <head-title title="报名设置" style="margin-bottom: 32px" />
        <a-form-item
          label="报名截止时间"
          v-bind="formItemLayout"
          name="signupEndTime"
          help="默认报名开始时间为比赛开始时间"
          :rules="[
            {
              required: true,
              message: '请输入报名截止时间',
              trigger: 'change',
            },
          ]"
        >
          <a-date-picker v-model:value="formData.signupEndTime" value-format="YYYY-MM-DD" :disabled-date="disabledDate" :show-today="false" :allow-clear="false" style="width: 240px" @change="signupChange" />
        </a-form-item>
        <a-form-item label="不允许同时报名的比赛" v-bind="formItemLayout" name="rejectNames">
          <a-select v-model:value="formData.rejectNames" mode="multiple" :disabled="hasPublished" placeholder="请选择" :show-arrow="cptList.length > 0" not-found-content="" style="width: 596px" @change="selectCompetition">
            <a-select-option v-for="v in cptList" :key="v.competitionName">
              {{ v.competitionName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="发放算力豆数量" v-bind="formItemLayout" name="beanNum" help="请输入1至50000之间的整数">
          <a-input-number id="inputNumber" v-model:value="formData.beanNum" :disabled="hasPublished" :min="1" :precision="0" :max="50000" style="width: 240px" />
        </a-form-item>
        <a-form-item label="发放算力豆有效期" v-bind="formItemLayout" name="daynum"> <a-input-number id="inputNumber" v-model:value="formData.daynum" :disabled="hasPublished" :min="1" :precision="0" :max="365" style="width: 240px; margin-right: 8px" />天 </a-form-item>
        <a-form-item
          label="参赛协议"
          v-bind="formItemLayout"
          name="protocolvalue"
          :rules="[
            {
              required: true,
              validator: protocolVerify,
              trigger: ['blur', 'change'],
            },
          ]"
        >
          <richtextEditor v-model:value="formData.protocolvalue" :bind-id="'competition-signup-editor'" :url="richtextEditorUrl" style="width: 948px" @change="getRichtextEditor" />
        </a-form-item>
        <head-title title="团队设置" style="margin: 32px 0" />
        <a-form-item
          label="团队编辑截止时间"
          v-bind="formItemLayout"
          name="teamEditEndTime"
          help="截止时间到期后，用户无法创建、解散团队或修改团队成员"
          :rules="[
            {
              required: true,
              message: '请输入团队编辑截止时间',
              trigger: 'change',
            },
          ]"
        >
          <a-date-picker v-model:value="formData.teamEditEndTime" value-format="YYYY-MM-DD" :disabled-date="disabledDate" :show-today="false" :allow-clear="false" style="width: 240px" @change="teamTimeChange" />
        </a-form-item>
        <a-form-item
          label="团队人数上限"
          v-bind="formItemLayout"
          style="margin-bottom: 6px"
          name="teamMax"
          help="不得小于当前已有团队人数上限"
          :rules="[
            {
              required: true,
              validator: teamMaxVerify,
              message: '不得小于当前已有团队人数上限',
              trigger: 'change',
            },
          ]"
        >
          <a-space>
            <a-input-number id="inputNumber" v-model:value="formData.teamMax" :min="1" :max="20" :precision="0" style="width: 240px" />
            <span>人</span>
          </a-space>
        </a-form-item>
        <a-form-item label=" " v-bind="formItemLayout">
          <a-space>
            <a-button type="primary" :disabled="nextDisabled" style="width: 120px" @click="handleSubmit"> 预览 </a-button>
            <a-button style="width: 88px" @click="handleCancelEdit()">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>
    <a-spin :spinning="spinning">
      <div v-show="isPreview" class="main">
        <head-title title="报名设置" style="margin-bottom: 24px" />
        <a-row v-for="itemKey in Object.keys(teamSignSettingMaps)" :key="itemKey" :gutter="[24, 16]">
          <a-col :span="4" style="text-align: right">{{ teamSignSettingMaps[itemKey] }}：</a-col>
          <a-col v-if="itemKey === 'beanNum'" :span="19" class="bean1">{{ formData.beanNum ? `${formData.beanNum}个` : '--' }}</a-col>
          <a-col v-else-if="itemKey === 'signupEndTime'" :span="19">{{ handleTime(formData.signupEndTime) }}</a-col>
          <a-col v-else-if="itemKey === 'rejectNames'" :span="19">{{ formData.rejectNames.length > 0 ? formData.rejectNames.join('；') : '--' }}</a-col>
          <a-col v-else-if="itemKey === 'daynum'" :span="19">{{ formData.daynum ? `${formData.daynum}天` : '--' }}</a-col>
          <a-col v-else-if="itemKey === 'protocolvalue'" :span="19">
            <div v-if="textHtml" class="html-content cpt-explain markdown-body" v-html="textHtml"></div>
            <div v-else>--</div>
          </a-col>
          <a-col v-else :span="20">{{ formData[itemKey] }}</a-col>
        </a-row>
        <div style="margin: 20px 0px 46px">
          <head-title title="团队设置" style="margin-bottom: 24px" />
          <a-row v-for="itemKey in Object.keys(teamSettingMaps)" :key="itemKey" :gutter="[24, 16]">
            <a-col :span="4" style="text-align: right">{{ teamSettingMaps[itemKey] }}：</a-col>
            <a-col v-if="itemKey === 'teamMax'" :span="19">{{ formData[itemKey] }}人</a-col>
            <a-col v-else-if="itemKey === 'teamEditEndTime'" :span="19">{{ handleTime(formData.teamEditEndTime) }}</a-col>
            <a-col v-else :span="19">{{ formData[itemKey] }}</a-col>
          </a-row>
          <a-row style="margin-top: 32px">
            <a-space>
              <a-button type="primary" style="width: 120px" @click="saveAndPublish"> {{ hasPublished ? '保存并发布' : '保存' }} </a-button>
              <a-button style="width: 88px" @click="isPreview = false">返回编辑</a-button>
            </a-space>
          </a-row>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRoute, useRouter } from 'vue-router';
import headTitle from '@/components/headTitle';
import API from '@/constants/api/API.js';
import { teamSignSettingMaps, teamSettingMaps, publishStatusKeys } from '../../../competitionConfig/index.js';
import richtextEditor from '@/components/richtextEditor.vue';
import dayjs from 'dayjs';
import { dateConvert, dateEndTime } from '@/utils/utils';
import { textUpload } from '@/apis/teaching.js';

const store = useStore();
const route = useRoute();
const router = useRouter();

const spinning = ref(false);
const isPreview = ref(false);
const formItemLayout = reactive({
  labelCol: {
    xs: { span: 7 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 18 },
    sm: { span: 12 },
  },
});
const cptList = ref([]);
const teamMaxNum = ref('');
const richtextEditorUrl = ref('');
const textHtml = ref('');
const editForm = ref();

const formData = reactive({
  signupEndTime: undefined,
  rejectNames: [],
  rejectCids: [],
  beanNum: '',
  daynum: '',
  protocolvalue: '',
  teamEditEndTime: undefined,
  teamMax: '',
});

const competitionId = computed(() => route.params.competitionId);
const currentManageCompetition = computed(() => store.state.competition.currentManageCompetition);
const hasPublished = computed(() => currentManageCompetition.value.releaseSta === publishStatusKeys.PUBLISHED);

const nextDisabled = computed(() => {
  let { signupEndTime, protocolvalue, teamEditEndTime, teamMax } = formData;
  return !(signupEndTime && protocolvalue.trim() && teamEditEndTime && teamMax && teamMax >= teamMaxNum.value);
});

watch(isPreview, () => {
  window.scrollTo(0, 0);
});

function getCptInfo() {
  API.competition_model.getCompetitionManageEditJoinTeam({ cid: competitionId.value }).then((res) => {
    if (res.state === 'OK' && res.body) {
      for (const key in res.body) {
        if (res.body[key]) {
          formData[key] = res.body[key];
          if (key == 'protocolvalue') {
            richtextEditorUrl.value = res.body[key];
          }
        }
      }
    }
  });
  // 团队已有人数
  API.competition_model.getCompetitionTeamMemberMaxNum({ cid: competitionId.value }).then((res) => {
    if (res.state === 'OK' && res.body) {
      teamMaxNum.value = res.body;
    }
  });
  // 比赛开始&结束时间
  API.competition_model.getCompetitionManageInfo({ cid: competitionId.value }).then((res) => {
    if (res.state === 'OK' && res.body) {
      competitionBeginTime.value = res.body.startTime;
      competitionEndTime.value = res.body.endTime;
      // 可选择比赛列表
      if (!hasPublished.value) {
        API.competition_model.getCompetitionNames({ cid: competitionId.value }).then((res) => {
          if (res.state === 'OK' && res.body) {
            cptList.value = res.body;
          }
        });
      }
      // 报名/团队编辑截至默认时间
      if (!formData.signupEndTime) {
        formData.signupEndTime = res.body.endTime;
      }
      if (!formData.teamEditEndTime) {
        formData.teamEditEndTime = res.body.endTime;
      }
    }
  });
}

const competitionBeginTime = ref('');
const competitionEndTime = ref('');

function handleTime(t) {
  return dateConvert(t).replace(/\//g, '.');
}

function getRichtextEditor(val) {
  formData.protocolvalue = val;
  editForm.value.validateFields(['protocolvalue']);
}

function signupChange(date, dateString) {
  formData.signupEndTime = dateString;
}

function teamTimeChange(date, dateString) {
  formData.teamEditEndTime = dateString;
}

function selectCompetition(valueList) {
  formData.rejectCids = [];
  for (let j = 0; j < valueList.length; j++) {
    for (let i = 0; i < cptList.value.length; i++) {
      if (cptList.value[i].competitionName == valueList[j]) {
        formData.rejectCids.push(cptList.value[i].cid);
      }
    }
  }
}

function protocolVerify(rule, value, callback) {
  if (value?.trim().length == 0) {
    callback('请输入');
  }
  callback();
}

function teamMaxVerify(rule, value, callback) {
  if (value < teamMaxNum.value) {
    callback(new Error());
  } else {
    callback();
  }
}

function disabledDate(time) {
  const beginTime = dayjs(competitionBeginTime.value);
  const endTime = dayjs(competitionEndTime.value);
  return time < beginTime.subtract(1, 'day') || time > endTime;
}

function handleSubmit() {
  editForm.value
    .validate()
    .then(() => {
      textHtml.value = formData.protocolvalue;
      isPreview.value = true;
    })
    .catch((err) => {
      throw new Error(err);
    });
}

function handleCancelEdit(noLeaveConfirm) {
  router.push({
    path: `/competition/competition-management/${competitionId.value}`,
    query: {
      tabId: '3',
      subtabId: '1',
      noLeaveConfirm,
    },
  });
}

function saveAndPublish() {
  spinning.value = true;
  let signupEndTime = new Date(dateEndTime(formData.signupEndTime)).toISOString();
  let teamEditEndTime = new Date(dateEndTime(formData.teamEditEndTime)).toISOString();
  textUpload({ baseString: formData.protocolvalue }).then((res) => {
    if (res.state === 'OK') {
      let joinTeamInfo = {
        signupEndTime,
        teamEditEndTime,
        rejectNames: formData.rejectNames.length > 0 ? formData.rejectNames : null,
        rejectCids: formData.rejectCids.length > 0 ? formData.rejectCids : null,
        beanNum: formData.beanNum,
        daynum: formData.daynum,
        protocolvalue: res.body.url,
        teamMax: formData.teamMax,
        cid: competitionId.value,
      };
      API.competition_model.getCompetitionTeamSaveAndUpdate(joinTeamInfo).then((res1) => {
        if (res1.state === 'OK' && res1.body != null && res1.body == 'success') {
          handleCancelEdit(true);
          spinning.value = false;
          window.$message?.success?.('编辑报名及团队设置成功') || window.message?.success?.('编辑报名及团队设置成功');
        } else {
          spinning.value = false;
          window.$message?.error?.('编辑报名及团队设置失败') || window.message?.error?.('编辑报名及团队设置失败');
        }
      });
    }
  });
}

onMounted(() => {
  getCptInfo();
});
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.edit-header {
  .competition-edit-header();
}
.main {
  padding: 32px;
  color: #121f2c;
  .cpt-explain {
    color: #606972;
    white-space: pre-line;
  }
  :deep(.ant-form-item-label > label) {
    color: #121f2c;
  }
  :deep(.ant-form-explain) {
    font-size: 12px;
  }
  :deep(.ant-col) {
    padding: 12px !important;
  }
  .ant-form-item {
    margin-bottom: 8px;
    :deep(.ant-col-sm-3) {
      width: 15.5%;
    }
  }
  .bean1 {
    height: 18px;
    font-size: 20px;
    font-weight: @jt-font-weight-medium;
    color: #f17506;
    line-height: 18px;
  }
}
</style>
