<template>
  <div class="wrap">
    <h1>{{ preview ? '预览' : '编辑' }}结果文件提交设置</h1>
    <a-form v-show="!preview" ref="formRef" v-bind="formItemLayout" :model="resultData" class="form-wrap" :colon="false">
      <a-row class="row" :gutter="rowGutter">
        <a-col :span="12">
          <a-form-item label="提交开放时间" help="需在比赛起止时间段内，提交结束后不允许解散团队" name="rangePicker" :rules="[{ type: 'array', required: true, message: '需在比赛起止时间段内', trigger: ['blur', 'change'] }]">
            <a-range-picker v-model:value="resultData.rangePicker" :disabled-date="disabledDate">
              <!-- <template #suffixIcon>
                <CalendarOutlined />
              </template> -->
            </a-range-picker>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="提交方式" style="display: flex" class="checkbox-group" name="submitType" :rules="[{ required: true, message: '请选择', trigger: ['blur', 'change'] }]">
            <a-checkbox-group v-model:value="resultData.submitType" style="width: 100%">
              <a-row>
                <a-col :span="8">
                  <a-checkbox :value="2"> 本地上传 </a-checkbox>
                </a-col>
                <a-col :span="16">
                  <a-checkbox :value="3"> 从团队共享存储空间中选择 </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="row" :gutter="rowGutter">
        <a-col :span="12">
          <a-form-item label="自动评分" name="markType" :rules="[{ required: true, message: '请选择', trigger: ['blur', 'change'] }]">
            <a-switch v-model:value="resultData.markType" :disabled="disableEditByListTotal" checked-children="开" un-checked-children="关" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="每日提交次数上限" name="teamSubmitMax" :rules="[{ required: true, message: '请输入正整数', trigger: ['blur', 'change'] }]">
            <a-input-number v-model:value="resultData.teamSubmitMax" :precision="0" :step="1" :min="0" :max="100" style="width: 240px" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row v-if="resultData?.markType" class="row" :gutter="rowGutter">
        <a-col :span="12">
          <a-form-item label="指标名称" help="可输入多个指标名称" name="scoreName" :rules="[{ required: true, validator: inputTagValidator, trigger: ['blur', 'change'] }]">
            <inputTag :disabled="disableEditByListTotal" :max="10" @change="getInputTag" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="排序方式" name="scoreFlag" :rules="[{ required: true, message: '请选择', trigger: ['blur', 'change'] }]">
            <a-radio-group v-model:value="resultData.scoreFlag" :disabled="disableEditByListTotal">
              <a-radio :value="true"> 指标越高越靠前 </a-radio>
              <a-radio :value="false"> 指标越低越靠前 </a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="row" :gutter="[24, 8]">
        <a-col :span="24">
          <a-form-item label="提交要求" :label-col="{ span: 3 }" name="richtextEditor" :rules="[{ required: true, validator: richtextEditorValidator, trigger: ['blur', 'change'] }]">
            <richtextEditor v-model:value="resultData.richtextEditor" bind-id="competition-result-editor" :url="richTextUrl" @change="getRichtextEditor" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="row" :gutter="rowGutter">
        <a-col :offset="3">
          <div class="button-group">
            <a-button type="primary" class="button" @click="gotoPreview"> 预览 </a-button>
            <a-button type="default" class="button" @click="gobackBtn()"> 取消 </a-button>
          </div>
        </a-col>
      </a-row>
    </a-form>
    <div v-show="preview" class="result-content">
      <resultContentVue v-if="preview" :preview-details="previewDetails" />
      <div class="descriptions-item left">
        <div class="title"></div>
        <div class="content">
          <div class="button-group">
            <a-button type="primary" class="button" :loading="loading" @click="submit">{{ hasPublished ? '保存并发布' : '保存' }} </a-button>
            <a-button type="default" class="button" :disabled="loading" @click="preview = false"> 返回编辑 </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
// import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import inputTag from './input-tag.vue';
import richtextEditor from '@/components/richtextEditor.vue';
import API from '@/constants/api/API';
import { textUpload } from '@/apis/teaching';
import resultContentVue from '../result/resultContent.vue';
import { publishStatusKeys } from '../../../../competitionConfig';
dayjs.locale('zh-cn');

const route = useRoute();
const router = useRouter();
const store = useStore();

const formRef = ref();
const disableEditByListTotal = ref(+route.query.listTotal !== 0);
const resultData = reactive({
  cid: route.params.competitionId || '',
  rangePicker: [],
  submitType: [],
  markType: false,
  teamSubmitMax: null,
  scoreName: [],
  scoreFlag: true,
  richtextEditor: '',
});
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
const richTextUrl = ref('');
const preview = ref(false);
const loading = ref(false);
const previewDetails = reactive({
  cid: 0,
  markType: '',
  resultEndTime: '',
  resultNarrate: '',
  resultStartTime: '',
  scoreFlag: '',
  scoreName: [],
  submitType: '',
  teamSubmitMax: '',
});

const rowGutter = computed(() => [24, 0]);
const currentManageCompetition = computed(() => store.state.competition.currentManageCompetition);
const hasPublished = computed(() => currentManageCompetition.value.releaseSta === publishStatusKeys.PUBLISHED);

onMounted(() => {
  API.competition_model.fileSubmitGet({ cid: route.params.competitionId }).then((res) => {
    if (res.state === 'OK') {
      richTextUrl.value = res.body.resultNarrate;
      resultData.rangePicker = [dayjs(res.body?.resultStartTime ?? currentManageCompetition.value.StartTime), dayjs(res.body?.resultEndTime ?? currentManageCompetition.value.EndTime)];
      resultData.markType = res.body.markType;
      resultData.submitType = res.body.submitType ? (res.body.submitType === 1 ? [2, 3] : [res.body.submitType]) : [];
      resultData.teamSubmitMax = res.body.teamSubmitMax;
      resultData.scoreName = res.body.scoreName;
      resultData.scoreFlag = res.body.scoreFlag;
      resultData.richtextEditor = res.body.richtextEditor;
    }
  });
});

function getInputTag(val) {
  formRef.value.validateFields(['scoreName']);
  resultData.scoreName = [...val];
}
function getRichtextEditor(val) {
  resultData.richtextEditor = val;
  formRef.value.validateFields(['richtextEditor']);
}
function inputTagValidator(rule, value, callback) {
  if (value?.length <= 0) {
    callback('请输入');
  }
  callback();
}
function richtextEditorValidator(rule, value, callback) {
  if (value?.trim().length <= 0) {
    callback('请输入');
  }
  callback();
}
function gobackBtn(noLeaveConfirm) {
  router.push({
    path: `/competition/competition-management/${route.params.competitionId}`,
    query: { tabId: 5, subtabId: 1, noLeaveConfirm },
  });
}
function gotoPreview() {
  formRef.value
    .validate()
    .then((values) => {
      if (values) {
        Object.assign(previewDetails, {
          cid: route.params.competitionId,
          markType: resultData.markType,
          resultStartTime: resultData.rangePicker[0].startOf('day').format(),
          resultEndTime: resultData.rangePicker[1].endOf('day').format(),
          resultNarrate: resultData.richtextEditor,
          scoreFlag: resultData.scoreFlag,
          scoreName: resultData.scoreName,
          submitType: resultData.submitType.length === 1 ? resultData.submitType[0] : 1,
          teamSubmitMax: resultData.teamSubmitMax,
        });
        preview.value = true;
      }
    })
    .catch((err) => {
      if (!(err && Object.keys(err).length === 1 && err.scoreName && !resultData.markType)) {
        throw new Error(err);
      }
      throw new Error(err);
    });
}
function submit() {
  loading.value = true;
  textUpload({ baseString: previewDetails.resultNarrate })
    .then((res) => {
      if (res.state === 'OK') {
        const params = { ...previewDetails };
        params.resultNarrate = res.body.url;
        API.competition_model.resultSubmitUpdate(params).then((result) => {
          loading.value = false;
          if (result.state === 'OK') {
            window.$message.success(`编辑结果文件提交设置成功`);
            router.push({ path: `/competition/competition-management/${route.params.competitionId}`, query: { tabId: 5, subtabId: 1, noLeaveConfirm: true } });
          } else {
            window.$message.error(`编辑结果文件提交设置失败`);
          }
        });
      } else {
        loading.value = false;
        window.$message.error(`编辑结果文件提交设置失败`);
      }
    })
    .catch((err) => {
      loading.value = false;
      window.$message.error(`编辑结果文件提交设置失败`);
      throw new Error(err);
    });
}
function disabledDate(current) {
  return current && (current < dayjs(currentManageCompetition.value.StartTime).endOf('day') || current > dayjs(currentManageCompetition.value.EndTime).endOf('day'));
}
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
@import '~@/views/competition/competitionManagement/competitionRank/form-common.less';

.wrap {
  h1 {
    .competition-edit-header();
  }
}
.form-wrap {
  padding: 32px 0 64px;
}
.checkbox-group {
  :deep(.ant-form-item-children) {
    display: flex;
  }
  :deep(.ant-form-item-control) {
    height: 21px;
  }
}
.button-group {
  display: flex;
  .button {
    &:nth-of-type(1) {
      width: 120px;
      margin-right: 8px;
    }
    &:nth-of-type(2) {
      width: 88px;
    }
  }
}
.result-content {
  padding: 0 32px 64px;
  .button-group {
    margin-left: 114px;
  }
}
</style>
