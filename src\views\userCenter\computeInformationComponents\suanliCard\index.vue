<template>
  <div>
    <div class="suanli-swiper swiper-no-swiping">
      <ul class="suanli-card swiper-wrapper">
        <li v-if="signGainSuanliSwitch" class="swiper-slide suanli-list" style="padding: 0">
          <sign-gain-suanli></sign-gain-suanli>
        </li>
        <li v-if="giveBeansByCourse.feature" class="swiper-slide suanli-list course-suanli">
          <h6 class="title">课程陆续上新，学习赢算力</h6>
          <p class="text">新学期，新课程</p>
          <a-button type="primary" class="course-suanli-btn" @click="courseDetail">查看活动详情</a-button>
          <img src="@/assets/image/compute/course-suanli.png" class="img" alt="" />
        </li>
        <li class="swiper-slide suanli-list">
          <h6 class="title">验证邀请码，充值算力豆</h6>
          <p class="text">邀请码按需定向发放</p>
          <a-button type="primary" @click="dlgVisible = true">验证邀请码</a-button>
          <img src="@/assets/image/compute/code.png" class="img" alt="" />
        </li>
        <li v-if="marketingFeature" class="swiper-slide suanli-list">
          <h6 class="title">邀请新用户注册，赢取算力豆</h6>
          <p class="text">
            每邀请<span class="num">1</span>人，赢取<span class="num">{{ inviteInfo.beanCount || 0 }}</span
            >个算力豆
          </p>
          <a-button type="primary" @click="handleInvite">查看活动详情</a-button>
          <img src="@/assets/image/compute/register.png" class="img" alt="" />
        </li>
        <li v-if="(isEcloudCampaignSwitch && isHasEcloudAccount) || (isEcloudCampaignSwitch && !isHasEcloudAccount) || ((!isEcloudCampaignSwitch || isEcloudCampaignSwitch) && suanLiState.joinSta == ORDER_ECLOUD_JOIN_STATE.JOINED_IS_OPEN) || (!isEcloudCampaignSwitch && suanLiState.joinSta == ORDER_ECLOUD_JOIN_STATE.JOINED_CLOSED)" class="swiper-slide suanli-list">
          <div>
            <h6 class="title">订购移动云，赢取算力豆</h6>
            <p class="text">
              成功订购，赢取<span class="num">{{ suanLiBeanNum }}</span
              >个算力豆
            </p>
            <a-button type="primary" :loading="hasPhoneNumLoading" @click="toOrderEcloudComputing">查看活动详情</a-button>
            <img src="@/assets/image/compute/order.png" class="img" alt="" />
          </div>
        </li>
      </ul>
    </div>
    <invitation-code-modal :dlg-visible="dlgVisible" @closeInvitationCodeModal="dlgVisible = false" @getDetail="getDetail"></invitation-code-modal>
    <invitation-new-user-modal :invite-dlg-visible="inviteDlgVisible" :invite-modal-loading="inviteModalLoading" :invite-info="inviteInfo" :invitees="invitees" @closeInvitationNewUserModal="inviteDlgVisible = false"></invitation-new-user-modal>
    <order-mobile-cloud-modal :order-ecloud-computing-visible="orderEcloudComputingVisible" :unable-join-visible="unableJoinVisible" :not-open-visible="notOpenVisible" @closeOrderVisible="closeOrderVisible" @closeUnableJoinVisible="closeUnableJoinVisible"></order-mobile-cloud-modal>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex';
import { GET } from '@/request';
import InvitationCodeModal from '../modal/InvitationCodeModal.vue';
import InvitationNewUserModal from '../modal/InvitationNewUserModal.vue';
import OrderMobileCloudModal from '../modal/OrderMobileCloudModal.vue';
import signGainSuanli from '../../signGainSuanli/index.vue';
import Swiper from 'swiper';
import 'swiper/css/swiper.css';
import { ORDER_ECLOUD_JOIN_STATE_LIST, ORDER_ECLOUD_JOIN_STATE, ECLOUD_CAMPAIGN_SWITCH_STATE, ECLOUD_ACCOUNT, ECLOUD_PHONE_NUMBER } from '@/common/ecloud';
export default {
  components: {
    signGainSuanli,
    InvitationCodeModal,
    InvitationNewUserModal,
    OrderMobileCloudModal,
  },
  emits: ['getDetail', 'changeActiveTab'],
  data() {
    return {
      ORDER_ECLOUD_JOIN_STATE,
      dlgVisible: false, //验证邀请码弹窗
      marketingFeature: false,
      inviteInfo: {},
      invitees: [],
      inviteModalLoading: false,
      inviteDlgVisible: false,
      hasPhoneNumLoading: null,
      suanLiBeanNum: '',
      suanLiMessage: {},
      orderEcloudComputingVisible: false, // 订购移动云 是否填写手机号
      unableJoinVisible: false, // 订购移动云 无法参加
      notOpenVisible: false,
      ecloudCampaignStatus: false,
    };
  },
  computed: {
    ...mapState('suanLiBean', ['signGainSuanliSwitch']),
    ...mapState(['userInfo', 'suanLiState', 'giveBeansByCourse']),
    isHasPhoneNum() {
      return this.suanLiMessage.hasPhoneNum === ECLOUD_PHONE_NUMBER.HAVE;
    },
    isEcloudCampaignSwitch() {
      return this.suanLiMessage.ecloudCampaignSwitch === ECLOUD_CAMPAIGN_SWITCH_STATE.OPEN;
    },
    isHasEcloudAccount() {
      return this.suanLiMessage.hasEcloudAccount === ECLOUD_ACCOUNT.HAVE;
    },
  },
  mounted() {
    this.getSwiper();
    this.getInvitationFeature();
    this.initInvitationMessage();
    this.getEcloudCampaignStatus();
    this.setSignGainSuanliSwitch();
  },
  methods: {
    ...mapActions('suanLiBean', ['setSignGainSuanliSwitch']),
    courseDetail() {
      this.$router.push('course/course-list');
    },
    getDetail() {
      this.$emit('getDetail');
    },

    getInvitationFeature() {
      GET('/marketing/web/getInvitationFeature', {}).then((res) => {
        if (res.state === 'OK') {
          this.marketingFeature = res.body;
        } else {
          this.marketingFeature = false;
        }
      });
    },
    initInvitationMessage() {
      GET('/marketing/web/getInvitationMessage', {}).then((res) => {
        if (res.state === 'OK') {
          this.inviteInfo = res.body;
          this.invitees = res.body.invitees || [];
        }
      });
    },
    handleInvite() {
      this.getInviteInfo();
    },
    getInviteInfo() {
      this.inviteModalLoading = true;
      this.inviteDlgVisible = true;
      GET('/marketing/web/getInvitationMessage', {}).then((res) => {
        if (res.state === 'OK') {
          this.inviteInfo = res.body;
          this.invitees = res.body.invitees || [];

          this.inviteModalLoading = false;
        } else {
          this.inviteModalLoading = false;
          this.inviteDlgVisible = false;
          this.$notification.error({
            message: '邀请注册失败',
            description: '登录信息已过期，请刷新后重试',
          });
        }
      });
    },
    // 获取算力豆
    getSuanli() {
      GET('/marketing/web/ecloud/get').then((res) => {
        let suanliData = {};
        if (res.state === 'OK') {
          suanliData = res.body || {};
        }
        this.suanLiBeanNum = suanliData.beannum;
        this.$store.commit('SET_SUANLISTATE_DATA', suanliData);
        this.hasPhoneNumLoading = false;
      });
    },
    getEcloudCampaignStatus() {
      GET('/marketing/web/ecloud/getEcloudCampaignStatus').then((res) => {
        if (res.state === 'OK') {
          this.ecloudCampaignStatus = res.body;
          if (this.ecloudCampaignStatus) this.getSuanliMessage();
        } else {
          this.ecloudCampaignStatus = false;
        }
      });
    },

    getSuanliMessage() {
      GET('/marketing/web/ecloud/getMessage').then((res) => {
        if (res.state === 'OK') {
          this.suanLiMessage = res.body || {};
          this.suanLiBeanNum = res.body.beannum;
        } else {
          this.suanLiMessage = {};
        }
        if (this.isHasPhoneNum) {
          this.getSuanli();
          this.hasPhoneNumLoading = true;
        } else {
          this.hasPhoneNumLoading = false;
        }
      });
    },
    toOrderEcloudComputing() {
      /**
       * 未绑定毕昇手机号，跳转至个人中心个人信息编辑页
       * ecloudCampaignSwitch	活动开关，1=开，0=关
         hasEcloudAccount 是否有移动云账号（是否进入过活动流程），1=有，0=无
         hasPhoneNum 毕昇是否绑定手机号，1=有，0=无
         joinSta  报名状态(当前步骤)，4=活动开放，已订购过，5=已参加过活动，无论活动是否开放，6=活动关闭、进入过活动流程（已有移动云账号）
       */

      if (!this.isHasPhoneNum) {
        this.orderEcloudComputingVisible = true;
      } else {
        if (!ORDER_ECLOUD_JOIN_STATE_LIST.includes(this.suanLiState.joinSta)) {
          // 活动开放、进入过活动流程
          this.$router.push({
            path: '/user-center/order-mobilecloud-computing',
          });
        } else if (this.suanLiState.joinSta === ORDER_ECLOUD_JOIN_STATE.ORDER_OPEN || this.suanLiState.joinSta === ORDER_ECLOUD_JOIN_STATE.JOINED_IS_OPEN) {
          // 活动开放、毕昇绑定手机号此前已订购深度学习平台  // 已参加过活动，无论活动是否开放
          this.unableJoinVisible = true;
        } else if (this.suanLiState.joinSta === ORDER_ECLOUD_JOIN_STATE.JOINED_CLOSED) {
          // 活动关闭、进入过活动流程（已有移动云账号）
          this.notOpenVisible = true;
        }
      }
    },

    closeOrderVisible(text) {
      this.orderEcloudComputingVisible = false;
      if (text === 'ok') this.$emit('changeActiveTab');
    },
    closeUnableJoinVisible(text) {
      if (text === 'unableJoin') {
        this.unableJoinVisible = false;
      } else {
        this.notOpenVisible = false;
      }
    },
    getSwiper() {
      new Swiper('.suanli-swiper', {
        hideOnClick: true,
        observer: true,
        observeParents: true,
        slidesPerView: 'auto',
        navigation: {
          nextEl: '.next',
          prevEl: '.pre',
          disabledClass: 'button-disabled',
        },
      });
    },
  },
};
</script>
<style lang="less">
@import '~@/assets/styles/var.less';

.suanli-swiper {
  overflow: hidden;
}
.suanli-card {
  .swiper-slide {
    width: 365px;
  }
  .suanli-list {
    min-width: 365px;
    height: 142px;
    background: linear-gradient(180deg, #e8f6ff 0%, #f5fbff 100%);
    border-radius: 4px;
    padding: 24px;
    margin-right: 21px;
    position: relative;

    &:last-child {
      margin-right: 0px;
    }

    .img {
      width: 164px;
      height: 138px;
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  .title {
    font-size: @jt-font-size-lg;
    font-weight: 600;
    color: @jt-text-color-primary;
    line-height: 22px;
    margin-bottom: 8px;
  }
  .text {
    font-size: @jt-font-size-base;
    font-weight: @jt-font-weight;
    color: @jt-text-color;
    line-height: 20px;
    margin-bottom: 16px;
  }

  .course-suanli {
    background: linear-gradient(90deg, #ddfbf5 0%, #e8fefa 100%);

    .course-suanli-btn {
      background: #00c494;
      border: 1px solid #00c494;
      &:hover {
        background: #00b78a;
      }
    }
  }
}
.num {
  color: #f17506;
}
</style>
