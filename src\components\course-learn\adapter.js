import temp1 from './temp1';
import temp2 from './temp2';
import temp3 from './temp3';
import temp4 from './temp4';
import temp5 from './temp5';
import temp6 from './temp6';
import temp7 from './temp7';

export const weightMap = {
  video: 1,
  pdf: 2,
  jupyter: 4,
};

export const tempMap = [temp1, temp2, temp3, temp4, temp5, temp6, temp7];

export function tempSwitcher(type) {
  return tempMap[type - 1];
}
