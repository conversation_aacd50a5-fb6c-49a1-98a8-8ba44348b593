<template>
  <div class="page-box">
    <header>
      <div class="inner">
        <jt-breadcrumb class="jt-breadcrumb">
          <jt-breadcrumb-item>
            <router-link to="/course">学习</router-link>
          </jt-breadcrumb-item>
          <jt-breadcrumb-item>
            <router-link to="/course/course-list">全部公开课</router-link>
          </jt-breadcrumb-item>
          <jt-breadcrumb-item>{{ courseInfo.name }}</jt-breadcrumb-item>
        </jt-breadcrumb>
      </div>
    </header>
    <section>
      <div class="inner">
        <a-spin :spinning="loading" :tip="loadingTip">
          <div class="section-title">
            <img :src="courseInfo.imageUrl" class="img" alt="" />
            <div class="section-title-left">
              <div class="title">
                {{ courseInfo.name }}
                <span :class="getCourseStatus(courseInfo.courseStatus).className">{{ getCourseStatus(courseInfo.courseStatus).text }}</span>
              </div>
              <p>{{ courseInfo.courseIntroduce }}</p>
              <p>
                {{ instituteInfo.name }}
                <jt-divider type="vertical" />
                {{ courseInfo.catalogNum || 0 }}节
                <jt-divider type="vertical" />
                {{ courseInfo.studyNum || 0 }}人学习
                <jt-divider type="vertical" />
                开课时间：{{ courseInfo.startTime }} -{{ courseInfo.endTime }}
              </p>
              <div>
                <jt-button class="button" @click="gotoLearn" :disabled="learnStatusItem.disabled" type="primary">{{ learnStatusItem.label }}</jt-button>
              </div>
            </div>
          </div>
          <div class="section-content">
            <div class="section-left">
              <jt-tabs class="jt-tabs" v-model="tabsActiveKey">
                <jt-tab-pane key="1" tab="课程目录" style="margin-top: 16px">
                  <jt-content-with-empty :empty="courseCatalogs.length === 0">
                    <div v-for="(item, index) in courseCatalogs" :key="index" class="course-item" disabled>
                      <h6>{{ `第${toChinesNum(index + 1)}节：${item.catalogName}` }}</h6>
                    </div>
                  </jt-content-with-empty>
                </jt-tab-pane>
                <jt-tab-pane key="2" tab="课程介绍">
                  <jt-content-with-empty :empty="introductionInfoEmpty">
                    <div class="course-introduce-row">
                      <h6 style="margin-top: 32px">课程描述</h6>
                      <div class="w-e-text">
                        <div v-html="introductionInfo.courseDesc || '无'"></div>
                      </div>
                    </div>
                    <div class="course-introduce-row">
                      <h6>前置知识</h6>
                      <div class="w-e-text">
                        <div v-html="introductionInfo.courseFrontKnowledge || '无'"></div>
                      </div>
                    </div>
                    <div class="course-introduce-row">
                      <h6>课程目标</h6>
                      <div class="w-e-text">
                        <div v-html="introductionInfo.courseGoal || '无'"></div>
                      </div>
                    </div>
                  </jt-content-with-empty>
                </jt-tab-pane>
                <jt-tab-pane key="3" tab="课程资源" class="course-resource main-contents">
                  <jt-content-with-empty :empty="courseResourceDataEmpty">
                    <div class="item">
                      <h1 style="margin-top: 8px">资源基本信息</h1>
                      <p>算力需求：{{ courseResourceData.flag ? spec : '无' }}</p>
                      <p>代码量：{{ courseResourceData.codeNum || '无' }}</p>
                      <p>数据量：{{ courseResourceData.dataNum || '无' }}</p>
                      <p>九天能力支持</p>
                      <a-table v-if="platforms.length > 0" :data-source="platforms" :pagination="false" bordered size="middle">
                        <a-table-column key="platform" title="能力名称" data-index="platform" width="200px" />
                        <a-table-column key="desc" title="能力描述" data-index="desc" />
                        <a-table-column key="link" title="调用方式" data-index="link" width="100px">
                          <template slot-scope="link">
                            <a :href="link" target="_blank" rel="noopenner noreferrer">查看链接</a>
                          </template>
                        </a-table-column>
                      </a-table>
                      <span v-else>无</span>
                    </div>
                    <div class="item">
                      <h1>参考资料</h1>
                      <div class="w-e-text">
                        <div v-html="courseResourceData.courseData || '无'"></div>
                      </div>
                    </div>
                    <div class="item">
                      <h1>版权声明</h1>
                      <div class="w-e-text">
                        <div v-html="courseResourceData.courseCopyright || '无'"></div>
                      </div>
                    </div>
                  </jt-content-with-empty>
                </jt-tab-pane>
                <jt-tab-pane key="4" tab="教师团队">
                  <jt-content-with-empty :empty="courseTeacherList.length === 0">
                    <div class="teacher-team">
                      <div class="teacher-item" v-for="(item, index) in courseTeacherList" :key="index">
                        <img :src="item.teacherImage || defaultUrl" alt />
                        <h6>{{ item.teacherName }}</h6>
                        <p>{{ item.teacherDesc }}</p>
                      </div>
                    </div>
                  </jt-content-with-empty>
                </jt-tab-pane>
              </jt-tabs>
            </div>
            <div class="section-right">
              <div class="section-right-item section-right-top">
                <h6>授课机构</h6>
                <div class="logo">
                  <img :src="instituteInfo.imageUrl" alt />
                  <p>{{ instituteInfo.name }}</p>
                </div>
                <p>{{ instituteInfo.desc }}</p>
              </div>
              <div class="section-right-item section-right-bottom">
                <h6>相关课程</h6>
                <div class="course-relate-item" v-for="(x, index) in courseRelate" :key="index">
                  <div class="item" @click="gotoDetail(x)">
                    <img :src="x.courseImage" alt />
                    <h6>{{ x.courseName }}</h6>
                    <p>
                      {{ x.instituteName }}
                      <span>{{ x.courseStudyNum || 0 }}人学习</span>
                    </p>
                  </div>
                  <jt-divider class="jt-divider" v-if="index !== courseRelate.length - 1" />
                </div>
              </div>
            </div>
          </div>
        </a-spin>
      </div>
    </section>
  </div>
</template>

<script>
import { Breadcrumb as JtBreadcrumb, Button as JtButton, Divider as JtDivider, Tabs as JtTabs } from 'ant-design-vue';
import API from '@/constants/api/API.js';
import { courseStudentApi } from '@/apis';
import { getRichText, gotoConslePlatform } from '@/utils/utils';
import { checkLogin } from '@/keycloak';
import { getLocalConfig } from '@/config';
import { loadingTip } from '@/common/text';
import toChinesNum from '@/lib/toChinesNum';
import { getQueryByName } from '@/utils/utils.js';

const statusMap = {
  0: { label: '即将开始' },
  1: { label: '进行中' },
  2: { label: '已结束' },
};

const learnStatusMap = {
  0: { label: '未加入' },
  1: { label: '已加入' },
  2: { label: '已完成' },
};

const pubStatusMap = {
  0: { label: '未发布' },
  1: { label: '已发布' },
};
const jtMap = {
  1: { platform: '九天可视化建模平台', desc: '提供结构化数据的一站式机器学习服务，支持数据分析处理、模型训练和模型应用等功能', link: 'https://ecloud.10086.cn/home/<USER>/jiutianml' },
  2: { platform: '九天智能交互平台', desc: '面向于客服、助手、陪伴等人机交互场景提供智能会话能力的云服务', link: 'https://ecloud.10086.cn/home/<USER>/interactive' },
  3: { platform: '九天深度学习平台', desc: '提供CPU、V100、T4等高性能计算资源的调度管理，为模型训练、服务部署和在线推理提供一站式服务', link: 'https://ecloud.10086.cn/home/<USER>/jiutiandl' },
};

const specMap = {
  cpu: {
    key: '0',
    label: 'CPU',
  },
  vGpu: {
    key: '1',
    label: 'vGPU',
  },
};

export default {
  components: {
    JtBreadcrumb,
    JtBreadcrumbItem: JtBreadcrumb.Item,
    JtButton,
    JtDivider,
    JtTabs,
    JtTabPane: JtTabs.TabPane,
  },
  data() {
    return {
      openCourseLineArray: [], // 展开的课程目录
      courseInfo: {}, // 当前课程详情
      courseRelate: [], // 相关课程
      courseTeacherList: [], // 教师团队
      courseCatalogs: [], // 课程目录
      hasInstance: false,
      tabsActiveKey: '1', // tab默认选中 第一项 （解决从相关推荐进来选中的还是上一页选中的选项）
      courseId: '',
      instituteInfo: {},
      introductionInfo: {},
      courseResourceData: {},
      defaultUrl: require('@/assets/image/avatar_big.png'),
      specMap,
      loading: false,
      loadingTip: loadingTip,
    };
  },
  computed: {
    CONSOLE_URL() {
      return getLocalConfig('CONSOLE_URL');
    },
    spec() {
      return this.courseResourceData.flag
        .split(',')
        .map((item) => specMap[item].label)
        .join(',');
    },
    platforms() {
      const arr = this.courseResourceData.jiuFlag ? this.courseResourceData.jiuFlag.split(',') : [];
      return arr.map((item) => jtMap[item]);
    },
    courseResourceDataEmpty() {
      const fields = ['flag', 'codeNum', 'dataNum', 'jiuFlag', 'courseData', 'courseCopyright'];
      return !Object.keys(this.courseResourceData).some((key) => fields.includes(key) && this.courseResourceData[key]);
    },
    introductionInfoEmpty() {
      return !Object.values(this.introductionInfo).some((item) => !!item);
    },
    learnStatusItem() {
      let item = {};
      const statusLabel = statusMap[this.courseInfo.courseStatus || 0].label;
      if (statusLabel === '即将开始') {
        item = { label: statusLabel, disabled: true };
      } else if (statusLabel === '进行中') {
        if (learnStatusMap[this.courseInfo.courseStudentStudyFlag || 0].label !== '未加入') {
          item = { label: '前往学习' };
        } else {
          if (pubStatusMap[this.courseInfo.pubFlag || 0].label === '已发布') {
            item = { label: '加入课程' };
          } else {
            item = { label: '未发布', disabled: true };
          }
        }
      } else {
        item = { label: '已结束', disabled: true };
        if (learnStatusMap[this.courseInfo.courseStudentStudyFlag || 0].label !== '未加入') {
          item = { label: '前往学习' };
        }
        if (!this.$keycloak.authenticated) {
          item = { label: '已结束' };
        }
      }
      return item;
    },
  },
  created() {
    if (getQueryByName('idp_preferred')) {
      if (!checkLogin(true)) {
        return;
      }
    }
    //配合网大的校验规则
    if (getQueryByName('ticket')) {
      checkLogin(true);
    }
    this.pageInit();
  },
  // 监听,当路由发生变化的时候执行
  watch: {
    $route() {
      this.pageInit();
    },
  },
  methods: {
    toChinesNum,
    async getIntroductionInfo() {
      const courseDesc = await getRichText(this.courseInfo.courseDesc);
      const courseFrontKnowledge = await getRichText(this.courseInfo.courseFrontKnowledge);
      const courseGoal = await getRichText(this.courseInfo.courseGoal);
      this.introductionInfo = { courseDesc, courseFrontKnowledge, courseGoal };
    },
    pageInit() {
      this.courseId = this.$route.query.courseId;
      this.tabsActiveKey = '1';

      const formdata = {
        courseId: this.courseId,
      };
      this.loading = true;
      // 获取课程详情
      this.$GET('/course_model/web/course_student/course/courseById', formdata).then((res) => {
        this.loading = false;
        if (res.state === 'OK') {
          this.courseInfo = res.body;
          this.getInstituteInfo(this.courseInfo.instituteId);
          this.getIntroductionInfo();
        }
      });
      // 查询相关课程
      API.course_model.getCourseRelate(formdata).then((res) => {
        this.courseRelate = res.body;
      });
      // 查询课程详情教师团队
      API.course_model.getCourseDetailteachers(formdata).then((res) => {
        this.courseTeacherList = res.body;
      });
      // 查询课程详情目录
      courseStudentApi.getCatalogList(formdata).then((res) => {
        this.courseCatalogs = res.body;
        // 默认展开所有课程列表
        res.body.map((x, index) => {
          if (x.catalogDtos != null) {
            this.openCourseLine(index);
          }
        });
      });
      this.getCourseExt();
    },
    getCourseExt() {
      this.$GET('/course_model/web/course_student/courseExt/courseExt', { courseId: this.courseId }).then(async (res) => {
        this.courseResourceData = res.body || {};
        const courseCopyright = await getRichText(this.courseResourceData.courseCopyright);
        const courseData = await getRichText(this.courseResourceData.courseData);
        this.courseResourceData = { ...this.courseResourceData, courseCopyright, courseData };
      });
    },
    getInstituteInfo(id) {
      const obj = { instituteId: id };
      courseStudentApi.getInstituteInfo(obj).then((res) => {
        this.instituteInfo = res.body;
      });
    },
    /**
     * 点击判断显示隐藏某一行
     * @param {number} index 被点击的行的索引
     */
    openCourseLine(index) {
      if (this.openCourseLineArray.indexOf(index) === -1) {
        this.openCourseLineArray.push(index);
      } else {
        this.openCourseLineArray.splice([this.openCourseLineArray.indexOf(index)], 1);
      }
    },
    /**
     * 跳转 课程详情 页面
     * @param {object} item 被点击的课程
     */
    gotoDetail(item) {
      this.$router.push({
        path: '/course/course-detail',
        query: {
          courseId: item.courseId,
          num: item.courseStudyNum,
        },
      });
      this.openCourseLineArray = [];
    },
    gotoLearn() {
      if (!checkLogin(true)) {
        return;
      }
      if (!+this.courseInfo.courseStudentStudyFlag) {
        this.$GET('/course_model/web/course_student/student/studentAdd', { courseId: this.courseId }, { useError: true }).then((res) => {
          if (res.body) {
            this.$router.push(`/course/course-overview/${this.courseId}`);
          }
        });
      } else {
        this.$router.push(`/course/course-overview/${this.courseId}`);
      }
    },
    /**
     * 根据状态获取对应文案和classname
     * @param {String} status - 课程状态码
     * @returns {Object} 包含状态对应文案和class的对象
     */
    getCourseStatus(status) {
      const statusMap = {
        0: { text: '即将开始', className: 'ready' },
        1: { text: '进行中', className: 'running' },
        2: { text: '已结束', className: 'over' },
      };
      return statusMap[status] || { text: '', className: '' };
    },

    /**
     * 添加登录人学习课程
     */
    async getInstanceNumCreate(type) {
      await API.course_model.getInstanceNum({
        projectId: this.courseInfo.projectId,
        requestId: this.courseInfo.courseId,
      });

      const queryParams = new URLSearchParams({
        projectId: this.courseInfo.projectId,
        projectName: this.courseInfo.courseName,
      });

      const url = `${this.CONSOLE_URL}/home/<USER>/${type === 'gotoInstanceListPage' ? '' : 'instance-form?'}${queryParams.toString()}`;

      gotoConslePlatform(url);
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/var.less';

.w-e-text {
  :deep(ul),
  :deep(ol) {
    list-style: auto;
  }
}
.main-contents {
  .item {
    padding: 24px 0;
    h1 {
      margin: 0;
    }
    p {
      margin-bottom: 24px;
    }
  }
  h1 {
    padding-bottom: 16px;
    font-size: 16px;
    font-weight: @jt-font-weight-medium;
    color: #16161c;
  }
}
.section-left :deep(.ant-tabs-bar) {
  margin-bottom: 0;
}
// header
header {
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
}
.jt-breadcrumb {
  line-height: 56px;
}
// header end

// 面包屑
:deep(.ant-breadcrumb) {
  > span:nth-of-type(1) {
    color: #a0a6ab;
  }
  > span:nth-of-type(2) {
    color: #a0a6ab;
  }
  > span:nth-of-type(3) {
    color: #121f2c;
  }
}

// tab切换
:deep(.ant-tabs-top-bar) {
  margin: 0;
}
:deep(.ant-tabs-nav .ant-tabs-tab-active) {
  font-weight: @jt-font-weight-medium;
  color: #0082ff;
}
:deep(.ant-tabs-tab) {
  font-size: 16px;
  font-weight: 400;
  color: #121f2c;
}

// section
section {
  background: #f4f8fa;
  padding-bottom: 20px;
  margin-top: 20px;
}
.section-title {
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  padding: 32px;
  display: flex;
  // justify-content: space-between;
  img {
    width: 232px;
    height: 176px;
    margin-right: 32px;
    border-radius: 2px;
  }
}
.section-title-left {
  .title {
    font-size: 24px;
    line-height: 33px;
    font-weight: bold;
    display: flex;
    align-items: center;
    span {
      width: 60px;
      height: 24px;
      line-height: 24px;
      border-radius: 2px;
      border: 1px solid #0082ff;
      font-size: 12px;
      margin-left: 16px;
      text-align: center;
      &.running {
        color: #0082ff;
      }
      &.ready {
        border-color: #0cb0d4;
        color: #0cb0d4;
      }
      &.over {
        background: linear-gradient(270deg, #cbcfd2 0%, #cbcfd2 100%);
        border: none;
        color: #fff;
      }
    }
  }
  p {
    &:nth-of-type(1) {
      color: #a0a6ab;
      line-height: 20px;
      margin-top: 16px;
    }
    &:nth-of-type(2) {
      color: #606972;
      line-height: 20px;
      margin-top: 16px;
    }
  }
  .button {
    margin-top: 31px;
    width: 106px;
    height: 40px;
  }
}
.section-content {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}
.section-left {
  width: 900px;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  padding: 10px 32px 0;
}
.jt-tabs {
  padding-bottom: 20px;
}
// 课程目录
.course-item {
  box-shadow: 0px 1px 0px 0px #e0e1e1;
  h6 {
    font-weight: bold;
    line-height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: default;
    color: #121f2c;
    font-size: 14px;

    span {
      color: #606972;
    }
  }
  ul {
    margin-top: 4px;
    padding-bottom: 20px;
    text-indent: 32px;
    color: #606972;
    line-height: 20px;
    li {
      margin-bottom: 16px;
      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
}
// 课程目录 end
// 课程介绍
.course-introduce-row {
  line-height: 20px;
  h6 {
    font-size: 16px;
    font-weight: @jt-font-weight-medium;
    margin-top: 48px;
    color: #16161c;
  }
  p {
    color: #606972;
  }
}
// 课程介绍 end
// 课程资源
.course-resource {
  .title {
    font-size: 16px;
    font-weight: @jt-font-weight-medium;
    color: #16161c;
    line-height: 22px;
    margin-bottom: 16px;
    margin-top: 32px;
    &:not(:nth-of-type(1)) {
      margin-top: 48px;
    }
  }
  .text {
    font-size: 14px;
    font-weight: 400;
    color: #606972;
    line-height: 20px;
  }
}
// 课程资源 end
// 教师团队
.teacher-team {
  margin-top: 80px;
  display: flex;
  flex-wrap: wrap;
}
.teacher-item {
  width: 232px;
  height: 209px;
  text-align: center;
  margin-bottom: 48px;
  margin-right: 20px;
  &:nth-of-type(3n) {
    margin-right: 0;
  }
  img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: block;
    margin: auto;
  }
  h6 {
    margin-top: 32px;
    font-size: 18px;
    line-height: 25px;
  }
  p {
    color: #606972;
    line-height: 20px;
    margin-top: 12px;
  }
}
// 教师团队 end
.section-right-item {
  padding: 24px;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  h6 {
    font-size: 20px;
    font-weight: bold;
    line-height: 28px;
  }
}
.section-right-top {
  width: 280px;
  height: 295px;
  margin-bottom: 20px;
  margin-left: 20px;
  .logo {
    margin-top: 32px;
    text-align: center;
    img {
      width: 62px;
      height: 62px;
      margin: auto;
      display: block;
    }
    p {
      font-size: 16px;
      line-height: 22px;
      margin-top: 18px;
    }
  }
  > p {
    margin-top: 16px;
    color: #606972;
    line-height: 24px;
    text-align: center;
    // padding: 0px 4px;
    text-align: left;
  }
}
.section-right-bottom {
  width: 280px;
  margin-left: 20px;
  .item {
    cursor: pointer;
    margin-top: 24px;
    img {
      width: 232px;
      height: 176px;
    }
    h6 {
      margin-top: 24px;
      font-size: 16px;
      line-height: 22px;
    }
    p {
      margin-top: 8px;
      color: #606972;
      line-height: 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  .jt-divider {
    margin: 16px 0;
    background: #e0e1e1;
  }
}
// section end
</style>
