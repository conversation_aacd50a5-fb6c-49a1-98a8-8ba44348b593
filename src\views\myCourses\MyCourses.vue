<template>
  <div class="mycourses-section">
    <div class="mycourses-header jt-box-shadow">
      <div class="mycourses-breadcrumb">
        <a-breadcrumb>
          <a-breadcrumb-item> <router-link to="/course">学习</router-link></a-breadcrumb-item>
          <a-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
            <router-link v-if="index + 1 !== levelList.length" :to="item.path">{{ item.meta.title }}</router-link>
            <span v-else>{{ item.meta.title }}</span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      <div v-if="Object.keys(currentActiveCourse).length > 0" class="mycourses-status">
        <!-- 公开课：#0082FF ，封闭课：#0082FF ？-->
        <!-- 即将开始：#0CB0D4 ，进行中：#0082FF，已结束 ？ -->
        <!-- 已发布：#17BB85 审核中：#FF8415，未发布：#606972  #A0A6AB -->
        <div class="status-left">
          <span class="course-name">{{ currentActiveCourse.courseName }}</span>
          <!-- 判断当前选中课程的三个状态 -->
          <div class="course-type">{{ currentActiveCourse.courseFlag == '1' ? '公开课' : '封闭课' }}</div>
          <div :class="currentActiveCourse.courseStatus == '0' ? 'starting' : currentActiveCourse.courseStatus == '1' ? 'running' : 'ended'">{{ currentActiveCourse.courseStatus == '0' ? '即将开始' : currentActiveCourse.courseStatus == '1' ? '进行中' : '已结束' }}</div>
          <div :class="currentActiveCourse.coursePublish == '1' ? 'published' : currentActiveCourse.coursePublish == '2' ? 'verifying' : 'unpublish'">{{ currentActiveCourse.coursePublish == '1' ? '已发布' : currentActiveCourse.coursePublish == '2' ? '审核中' : '未发布' }}</div>
        </div>
        <a-button class="route-to-main" @click="toCoursePreview(currentActiveCourse.coursePublish)">{{ currentActiveCourse.coursePublish == '1' ? '查看课程主页' : '预览课程主页' }}<jt-icon type="iconbofang"></jt-icon></a-button>
      </div>
    </div>

    <!-- 课程列表和课程管理的入口 -->
    <router-view />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { openInNewTab } from '@/utils/utils';
export default {
  name: 'MyCourses',
  data() {
    return {
      levelList: [],
    };
  },
  computed: {
    ...mapState(['userInfo']),
    ...mapState('course', ['currentActiveCourse']),
  },
  watch: {
    $route: {
      handler() {
        this.levelList = this.$route.matched.filter((item) => item.meta && item.meta.title && item.path !== '/course/teaching/mycourses/');
      },
      immediate: true,
    },
    userInfo: {
      handler(item) {
        console.log(item, 'item', item.fullName);
        // 为0说明是直接刷新进入，不做处理
        // if (Object.keys(item).length === 0) {
        //   return;
        // }
        // if (!(item.fullName && item.introduction && item.image)) {
        //   this.$router.push({
        //     path: '/user-center?edit=true&redirectUrl=course/teaching/mycourses',
        //   });
        // }
      },
      immediate: true,
    },
  },
  methods: {
    toCoursePreview(coursePublish) {
      // 打开新链接跳转
      let routeUrl = this.$router.resolve({
        path: `/course/teaching/course-preview/${this.currentActiveCourse.courseId}`,
        query: {
          coursePublish: coursePublish,
        },
      });
      openInNewTab(routeUrl.href);
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
.mycourses-section {
  padding-bottom: 48px;
  background-color: #f4f8fa;
}

.mycourses-header {
  background-color: white;
  .mycourses-breadcrumb {
    width: 1200px;
    margin: auto;
    .ant-breadcrumb {
      height: 56px;
      line-height: 56px;
    }
  }
  .mycourses-status {
    display: flex;
    justify-content: space-between;
    width: 1200px;
    margin: 12px auto 0px;
    height: 64px;

    .status-left {
      display: flex;
      align-items: center;
      padding-bottom: 36px;

      div {
        height: 24px;
        line-height: 23px;
        padding: 0px 12px;
        margin-right: 8px;
        border-radius: 2px;
        font-size: 12px;
      }
      .course-name {
        font-size: 24px;
        font-weight: @jt-font-weight-medium;
        color: #121f2c;
        margin-right: 16px;
        padding-bottom: 5px;
      }
      .course-type,
      .running {
        border: 1px solid #0082ff;
        color: #0082ff;
      }
      .starting {
        border: 1px solid #0cb0d4;
        color: #0cb0d4;
      }
      .ended {
        background: linear-gradient(270deg, #cbcfd2 0%, #cbcfd2 100%);
        color: #ffffff;
      }
      .published {
        border: 1px solid #17bb85;
        color: #17bb85;
      }
      .verifying {
        border: 1px solid #ff8415;
        color: #ff8415;
      }

      .unpublish {
        border: 1px solid #a0a6ab;
        color: #606972;
      }
    }
    .route-to-main {
      width: 124px;
      background-color: #e5f3ff;
      color: #0082ff;
      border: none;
      &:hover {
        background-color: #dbeeff;
      }
      i {
        margin-left: 2px;
      }
    }
  }
}
</style>
