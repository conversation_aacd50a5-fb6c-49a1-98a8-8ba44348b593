<template>
  <div class="basic-form">
    <a-form ref="createForm" :model="formData" :colon="false" @submit="handleSubmit">
      <a-form-item
        label="课程名称"
        v-bind="formItemLayout"
        name="courseName"
        :rules="[
          {
            required: true,
            message: '20个字符以内',
            trigger: 'change',
            max: 20,
          },
          {
            validator: validateCourseName,
            trigger: 'change',
          },
        ]"
      >
        <a-input v-model:value="formData.courseName" placeholder="请输入课程名称" />
      </a-form-item>

      <a-form-item
        label="开课时间"
        :style="{ 'margin-bottom': '0px' }"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 21 }"
        :rules="{
          required: true,
        }"
      >
        <a-form-item
          name="startTime"
          :style="{ display: 'inline-block' }"
          :rules="{
            required: true,
            message: '请选择起始时间',
          }"
        >
          <a-date-picker v-model="formData.startTime" :disabled="startTimeDisable" :disabled-date="disabledStartDate" format="YYYY-MM-DD" placeholder="起始时间" @openChange="handleStartOpenChange" />
        </a-form-item>
        <span> — </span>
        <a-form-item
          name="endTime"
          :style="{ display: 'inline-block' }"
          :rules="{
            required: true,
            message: '请选择结束时间',
          }"
        >
          <a-date-picker v-model="formData.endTime" :disabled-date="disabledEndDate" :open="endOpen" format="YYYY-MM-DD" placeholder="结束时间" @openChange="handleEndOpenChange" />
        </a-form-item>
        <span class="extra-msg" style="padding-left: 12px; color: red">开始日期到期后不可修改</span>
      </a-form-item>

      <a-form-item
        label="开课范围"
        v-bind="formItemLayout"
        name="courseFlag"
        :rules="{
          required: true,
          message: '请选择开课范围',
        }"
      >
        <a-radio default-checked :disabled="true"> 公开课 </a-radio>
        <!-- 如果是修改课程信息则不可进行开课范围变更 -->
        <!-- <a-radio-group v-model="formData.courseFlag" :disabled="!!courseId" @change="handleCourseflagChange"> -->
        <!-- <a-radio :key="1" value="1" :disabled="!PREVIEW_AUTH()"> 公开课</a-radio> -->
        <!-- <a-tooltip placement="top">
            <template #title>
              <span>封闭课仅指定学生可加入</span>
            </template>
            <a-radio :key="2" value="2"> 封闭课</a-radio>
          </a-tooltip> -->
        <!-- </a-radio-group> -->
        <!-- <span v-if="!courseId" class="extra-msg" style="padding-left: 22px; color: red">创建成功后不可修改，请谨慎选择</span> -->
      </a-form-item>
      <a-form-item
        label="课程分类"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 21 }"
        name="categoryCode"
        :rules="{
          required: true,
          message: '请选择课程分类',
        }"
      >
        <a-radio-group v-model="formData.categoryCode">
          <a-radio v-for="(info, i) in basicCreateInfo.categoryCode" :key="i" :value="info.key"> {{ info.name }}</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        label="能力分级"
        v-bind="formItemLayout"
        name="levelCode"
        :rules="{
          required: true,
          message: '请选择能力分级',
        }"
      >
        <a-radio-group v-model="formData.levelCode">
          <a-radio v-for="(info, i) in basicCreateInfo.levelCode" :key="i" :value="info.key"> {{ info.name }}</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        class="intro-paragraph"
        v-bind="formItemLayout"
        label="一句话简介"
        name="courseIntroduce"
        :rules="{
          required: true,
          message: '50个字符以内',
          trigger: 'change',
          max: 50,
        }"
      >
        <!-- <div class="intro-paragraph-content"> -->
        <a-textarea v-model:value="formData.courseIntroduce" placeholder="请输入简介内容" />
        <span class="intro-size-total">{{ formData.courseIntroduce ? formData.courseIntroduce.length : 0 }}/50</span>
        <!-- </div> -->
      </a-form-item>
      <a-form-item
        v-bind="formItemLayout"
        label="开课机构"
        name="instituteName"
        :rules="{
          required: true,
          message: '24个字符以内',
          trigger: 'change',
          max: 24,
        }"
      >
        <a-input v-model:value="formData.instituteName" placeholder="请输入开课机构"> </a-input>
      </a-form-item>
    </a-form>
    <a-space class="form-btns">
      <a-button type="primary" style="width: 120px" :disabled="nextBtnDisable" @click="handleSubmit"> 下一步 </a-button>
      <a-button style="width: 88px" @click="handleCancelEdit">取消</a-button>
    </a-space>
  </div>
</template>

<script>
import moment from 'moment';
import { basicCreateInfo } from '../constants';
import { findCourseByName } from '@/apis/teaching.js';
import { mapState } from 'vuex';

export default {
  emits: ['handleEditImageUrl', 'changePage'],
  data() {
    return {
      courseId: this.$route.params.courseId,
      basicCreateInfo,
      courseRange: '1',
      courseNameRepeat: false,
      formItemLayout: {
        labelCol: {
          xs: { span: 6 },
          sm: { span: 3 },
        },
        wrapperCol: {
          xs: { span: 18 },
          sm: { span: 12 },
        },
      },
      formData: {
        courseName: '',
        time: [],
        courseFlag: '1',
        categoryCode: '',
        levelCode: '',
        courseIntroduce: '',
        instituteName: '',
        startTime: '',
        endTime: '',
      },
      endOpen: false,
      startTimeDisable: false,
    };
  },
  mounted() {
    this.setFormData(this.currentActiveCourse);
  },
  computed: {
    ...mapState(['userInfo']),
    ...mapState('course', ['currentActiveCourse']),
    nextBtnDisable() {
      if (this.formData.courseName && this.formData.courseName.length > 0 && this.formData.courseName.length <= 20 && this.formData.courseFlag != '' && this.formData.categoryCode != '' && this.formData.levelCode != '' && this.formData.startTime && this.formData.endTime && this.formData.courseIntroduce && this.formData.courseIntroduce.length > 0 && this.formData.courseIntroduce.length <= 50 && this.formData.instituteName && this.formData.instituteName.length > 0 && this.formData.instituteName.length <= 24 && this.courseNameRepeat == false) {
        return false;
      } else {
        return true;
      }
    },
  },
  watch: {
    currentActiveCourse(newValue) {
      this.setFormData(newValue);
    },
    // 初始化直接刷新页面的时候异步获取学校名称
    userInfo(newValue) {
      if (!this.formData.instituteName) {
        this.formData.instituteName = newValue.school;
      }
    },
  },
  methods: {
    handleCourseflagChange() {
      // 触发一次coursename表单项的校验，需要根据课程范围重新进行重名校验
      this.$refs.createForm.validateFields(['courseName']);
    },
    validateCourseName(rule, value, callback) {
      if (value !== '') {
        if (this.currentActiveCourse.courseName === value) {
          this.courseNameRepeat = false;
          callback();
        } else {
          findCourseByName({ courseName: value, courseFlag: this.formData.courseFlag }).then((res) => {
            if (res.state === 'OK') {
              if (res.body) {
                this.courseNameRepeat = false;
                callback();
              } else {
                this.courseNameRepeat = true;
                callback(new Error('已存在同名课程，请重新输入'));
              }
            } else {
              this.courseNameRepeat = false;
              callback();
            }
          });
        }
      } else {
        callback(new Error('课程名称不可为空'));
      }
    },
    disabledStartDate(startTime) {
      const endTime = this.formData.endTime;
      if (startTime && startTime <= moment().endOf('day').subtract(1, 'day')) {
        return true;
      }
      if (!startTime || !endTime) {
        return false;
      }
      // 判断大小的时候会计算到秒，只有start的天小于等于end的天则可选择
      return startTime.hours(1).valueOf() > endTime.hours(23).valueOf();
    },
    disabledEndDate(endTime) {
      const startTime = this.formData.startTime;
      if (endTime && endTime <= moment().endOf('day').subtract(1, 'day')) {
        return true;
      }
      if (!endTime || !startTime) {
        return false;
      }
      return startTime.valueOf() > endTime.valueOf();
    },
    handleStartOpenChange(open) {
      if (!open && !this.formData.endTime) {
        this.endOpen = true;
      }
    },
    handleEndOpenChange(open) {
      this.endOpen = open;
    },

    setFormData(newValue) {
      this.formData = {
        courseName: newValue.courseName,
        time: [newValue.startTime, newValue.endTime],
        startTime: newValue.startTime ? moment(newValue.startTime) : '',
        endTime: newValue.endTime ? moment(newValue.endTime) : '',
        courseFlag: '1',
        categoryCode: newValue.categoryCode,
        levelCode: newValue.levelCode,
        courseIntroduce: newValue.courseIntroduce,
        instituteName: newValue.instituteName ? newValue.instituteName : this.$store.state.userInfo.school,
      };

      // 如果开始日期过期，则开始日期不可修改
      if (newValue.startTime) {
        this.startTimeDisable = moment(newValue.startTime) <= moment().endOf('day');
      }
      if (newValue.courseImage) {
        this.$emit('handleEditImageUrl', newValue.courseImage);
      }
    },
    handleSubmit() {
      this.$refs.createForm
        .validate()
        .then(() => {
          this.$emit('changePage');
        })
        .catch((err) => {
          throw new Error(err);
        });
    },
    handleCancelEdit() {
      if (this.courseId) {
        // eslint-disable-next-line no-unused-vars
        const { subPage, ...params } = this.$route.params;
        this.$router.replace({
          name: '课程管理',
          params,
        });
      } else {
        this.$router.push(`/course/teaching/mycourses`);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.basic-form {
  text-align: left;
  .form-btns {
    margin-left: 124px;
    padding-top: 16px;
    padding-bottom: 64px;
  }
  .extra-msg {
    font-size: 12px;
    color: #a0a6ab;
    line-height: 34px;
  }

  .intro-paragraph {
    //.intro-paragraph-content {
    position: relative;
    .intro-size-total {
      position: absolute;
      bottom: -15px;
      right: 8px;
    }
    //}
  }
}
</style>
