<template>
  <footer v-if="!getPathMatchHeaderFooter">
    <div class="inner">
      <div class="footer-left">
        <div class="footer-logo">
          <img src="@/assets/image/home/<USER>" alt />
        </div>
        <div class="footer-left-text-container">
          <div class="footer-left-text" style="align-items:start;">
            <span>平台简介</span>
            <ul>
              <li>九天·毕昇平台由中国移动通信有限公司研究院九天团队打造，提供充沛的GPU算力、丰富的数据和学习实战资源</br>服务课程学习、比赛打榜、工作求职等全流程场景，并面向高校提供在线教学、科研开发的一站式解决方案</li>
            </ul>
          </div>
          <div class="footer-left-text">
            <span>快捷入口</span>
            <ul>
              <li>
                <a href="https://ecloud.10086.cn/home/<USER>/jiutiandl" target="_blank" rel="noopenner noreferrer">九天智算平台</a>
              </li>
              <em>|</em>
              <li>
                <a href="https://ecloud.10086.cn/home/<USER>/interactive" target="_blank" rel="noopenner noreferrer">智能交互平台</a>
              </li>
            </ul>
          </div>

          <div class="footer-left-text">
            <span>联系我们</span>
            <ul>
              <li>邮箱：<EMAIL></li>
              <li>地址：北京市西城区宣武门西大街32号中国移动创新大楼</li>
            </ul>
          </div>
        </div>
      </div>
      <div class="footer-right">
        <div class="QR-code">
          <img src="@/assets/image/home/<USER>" alt />
        </div>
        <p>九天人工智能公众号</p>
      </div>
    </div>
    <div class="inner copy">
      <a href="https://beian.miit.gov.cn" target="_blank" rel="noopenner noreferrer">京ICP备05002571号-5</a>
    </div>
  </footer>
</template>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';
footer {
  background: #153654;
  padding: 56px 0 20px;
  color: rgba(255, 255, 255, 0.6);
  .inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 50px;
  }
  .copy {
    padding-top: 20px;
    justify-content: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 0;
  }
}
.footer-logo {
  height: 101px;
  display: table-cell;
  vertical-align: middle;

  > img {
    width: 227px;
    height: 39px;
  }
}
.footer-left-text-container {
  display: flex;
  flex-direction: column;
  .footer-description {
    display: flex;
    padding-bottom: 24px;
    span {
      width: 92px;
      font-size: 16px;
      font-weight: bold;
      color: #ffffff;
    }
    p {
      margin: 0px 24px 0px 30px;
    }
  }
}
.footer-left-text {
  display: flex;
  align-items: center;

  span {
    display: inline-block;
    min-width: 64px;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    line-height: 22px;
    margin-right: 8px;
  }
  ul {
    display: flex;
    li {
      margin: 0 24px;
    }
  }
  &:not(:last-of-type) {
    margin-bottom: 24px;
  }
}
.footer-right {
  .QR-code {
    width: 126px;
    height: 126px;
    background-color: #fff;
    img {
      width: 126px;
      height: 126px;
    }
  }
  p {
    margin-top: 8px;
  }
}
</style>

<script>
export default {
  computed: {
    headerHaveBorder() {
      const noBorderPathArr = ['/home', '/course', '/certification', '/competition'];
      return !noBorderPathArr.find((x) => x === this.$route.path);
    },
    getPathMatchHeaderFooter() {
      return this.$route.name === '404' || this.$route.name === '401';
    },
  },
};
</script>
