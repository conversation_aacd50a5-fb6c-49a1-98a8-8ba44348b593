<template>
  <div class="contents">
    <div class="header">课程资源</div>
    <jt-common-content :empty="dataEmpty" :loading="loading">
      <div class="main-contents">
        <div class="item">
          <h1>资源基本信息</h1>
          <p>算力需求：{{ courseResourceData.flag ? spec : '无' }}</p>
          <p>代码量：{{ courseResourceData.codeNum || '无' }}</p>
          <p>数据量：{{ courseResourceData.dataNum || '无' }}</p>
          <p>九天能力支持</p>
          <a-table v-if="platforms.length > 0" :data-source="platforms" :pagination="false" bordered size="middle">
            <a-table-column key="platform" title="能力名称" data-index="platform" width="200px" />
            <a-table-column key="desc" title="能力描述" data-index="desc" />
            <a-table-column key="link" title="调用方式" data-index="link" width="100px">
              <template #default="{ link }">
                <a :href="link" target="_blank" rel="noopener noreferrer">查看链接</a>
              </template>
            </a-table-column>
          </a-table>
          <span v-else>无</span>
        </div>
        <div class="item">
          <h1>参考资料</h1>
          <div class="w-e-text">
            <div v-html="courseResourceData.courseData || '无'"></div>
          </div>
        </div>
        <div class="item">
          <h1>版权声明</h1>
          <div class="w-e-text">
            <div v-html="courseResourceData.courseCopyright || '无'"></div>
          </div>
        </div>
      </div>
    </jt-common-content>
  </div>
</template>

<script>
import { getRichText } from '@/utils/utils';

const jtMap = {
  1: { platform: '九天可视化建模平台', desc: '提供结构化数据的一站式机器学习服务，支持数据分析处理、模型训练和模型应用等功能', link: 'https://ecloud.10086.cn/home/<USER>/jiutianml' },
  2: { platform: '九天智能交互平台', desc: '面向于客服、助手、陪伴等人机交互场景提供智能会话能力的云服务', link: 'https://ecloud.10086.cn/home/<USER>/interactive' },
  3: { platform: '九天深度学习平台', desc: '提供CPU、V100、T4等高性能计算资源的调度管理，为模型训练、服务部署和在线推理提供一站式服务', link: 'https://ecloud.10086.cn/home/<USER>/jiutiandl' },
};

const specMap = {
  cpu: {
    key: '0',
    label: 'CPU',
  },
  vGpu: {
    key: '1',
    label: 'vGPU',
  },
};

export default {
  props: { courseId: String, preview: Boolean },
  data() {
    return {
      courseResourceData: {},
      specMap,
      loading: false,
    };
  },
  computed: {
    spec() {
      return this.courseResourceData.flag
        .split(',')
        .map((item) => specMap[item].label)
        .join(',');
    },
    dataEmpty() {
      const fields = ['flag', 'codeNum', 'dataNum', 'jiuFlag', 'courseData', 'courseCopyright'];
      return !Object.keys(this.courseResourceData).some((key) => fields.includes(key) && this.courseResourceData[key]);
    },
    platforms() {
      const arr = this.courseResourceData.jiuFlag ? this.courseResourceData.jiuFlag.split(',') : [];
      return arr.map((item) => jtMap[item]);
    },
  },
  mounted() {
    this.getCourseExt();
  },
  methods: {
    getCourseExt() {
      this.loading = true;
      const url = this.preview ? '/course_model/web/course_student/course/teacherCourseSource' : '/course_model/web/course_student/courseExt/courseExt';
      this.$GET(url, { courseId: this.courseId }).then(async (res) => {
        const courseResourceData = res.body || {};
        const courseCopyright = await getRichText(courseResourceData.courseCopyright);
        const courseData = await getRichText(courseResourceData.courseData);
        courseResourceData.courseCopyright = courseCopyright;
        courseResourceData.courseData = courseData;
        this.courseResourceData = { ...courseResourceData };
        this.loading = false;
      });
    },
    formateJtFlag(jtFlag = '') {
      const flag = (jtFlag || '').split(',');

      let res = '';
      if (jtFlag) {
        for (let i = 0; i < flag.length; i++) {
          if (i !== flag.length - 1) {
            res += jtMap[flag[i]] + ',';
          } else {
            res += jtMap[flag[i]];
          }
        }
      }
      return res;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/assets/styles/index.less';

.w-e-text {
  :deep(ul),
  :deep(ol) {
    list-style: auto;
  }
}

.contents {
  background: #fff;
  flex: 1;
  flex-direction: column;
  .header {
    padding: 24px 0 16px 32px;
    font-size: 18px;
    font-weight: @jt-font-weight-medium;
    color: #121f2c;
    border-bottom: 1px solid #e0e1e1;
  }
  .main-contents {
    padding: 0 32px;
    .item {
      margin-top: 32px;
      h1 {
        font-size: 16px;
        font-weight: @jt-font-weight-medium;
        color: #16161c;
        margin-bottom: 6px;
      }
      p {
        color: #606972;
        line-height: 20px;
        margin-bottom: 16px;
      }
    }
  }
}
:deep(.ant-table-thead) {
  background-color: #fafafa;
}
</style>
