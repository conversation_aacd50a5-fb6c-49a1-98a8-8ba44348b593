<template>
  <div class="submit-record">
    <!-- 结果文件记录 -->
    <head-title title="结果文件提交记录" />
    <jt-common-content :loading="result.loading" :empty="resultList.length == 0" :empty-style="{ height: '416px' }" empty-title="暂无数据">
      <a-table :pagination="false" class="competition-table" :columns="resultColumns" :data-source="resultList" :scroll="{ x: isRankinTitleBeyondFour ? (resultColumns.length - 1) * 150 + 170 : 0 }" :row-key="(r, i) => i.toString()">
        <template #submitstatus="{ submitstatus, record }">
          <a-tooltip placement="topLeft">
            <template #title>
              <span v-if="submitstatus == RESULT_FILES_STATE.FAIL && record.submitMsg != null">{{ record.submitMsg }}</span>
            </template>
            <span :class="SUBMIT_STATUS_CLASS[submitstatus]">
              {{ resultFileStatus(submitstatus) }}
            </span>
          </a-tooltip>
        </template>
      </a-table>
    </jt-common-content>
    <jt-pagination v-if="resultList.length !== 0" :page-size="result.pageSize" :page-num="result.pageNum" :total="result.total" @changePageSize="resultPageSizeChange" @changePageNum="resultListChange"></jt-pagination>
    <!-- 答辩材料记录 -->
    <head-title v-if="showButton" title="审查及答辩材料提交记录" style="margin-top: 40px" />
    <jt-common-content v-if="showButton" :loading="reply.loading" :empty="reply.list.length == 0" :empty-style="{ height: '416px' }" empty-title="暂无数据">
      <a-table :pagination="false" class="competition-table" :columns="replyColumns" :data-source="reply.list" :row-key="(r, i) => i.toString()">
        <template #submitstatus="{ submitstatus, record }">
          <a-tooltip placement="topLeft">
            <template #title>
              <span v-if="submitstatus == REPLY_RECORD_STATE.FAIL && record.submitMsg != null">{{ record.submitMsg }}</span>
            </template>
            <span :class="SUBMIT_STATUS_CLASS[submitstatus]">
              {{ replyMaterialStatus(submitstatus) }}
            </span>
          </a-tooltip>
        </template>
        <template #operation="{ record }">
          <a :class="{ 'click-disable': record.submitstatus != REPLY_RECORD_STATE.SUCCESS || !replyAllowDownLoad }" @click="getCptReplyGetUrl(record.id, record.submitstatus)">下载</a>
        </template>
      </a-table>
    </jt-common-content>
    <jt-pagination v-if="reply.list.length !== 0" :page-size="reply.pageSize" :page-num="reply.pageNum" :total="reply.total" @changePageSize="replyPageSizeChange" @changePageNum="replyListChange"></jt-pagination>
  </div>
</template>

<script>
import HeadTitle from '@/components/headTitle/index.vue';
import API from '@/constants/api/API.js';
const OFFICIAL_INNOVATE_COMPETITION = [3, 5];
import { SUBMIT_STATUS_CLASS, RESULT_FILES_STATE, REPLY_RECORD_STATE } from '../competitionConfig/index';

export default {
  components: {
    HeadTitle,
  },
  props: {
    teamHave: { type: Boolean, default: false },
    showButton: { type: Boolean, default: false },
    updateBegin: { type: Boolean, default: false },
    typeId: { type: Number, default: 0 },
  },
  data() {
    return {
      RESULT_FILES_STATE,
      REPLY_RECORD_STATE,
      SUBMIT_STATUS_CLASS,
      competitionId: this.$route.query.id,
      resultTitleLength: [],
      resultColumns: [],
      resultList: [],
      result: {
        loading: true,
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
      replyColumns: [],
      replyAllowDownLoad: false,
      reply: {
        loading: true,
        list: [],
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
      resultTimer: null,
      replyTimer: null,
    };
  },
  computed: {
    isRankinTitleBeyondFour() {
      return this.resultTitleLength > 4;
    },
  },
  created() {
    this.getResultList();
    this.getReplyList();
  },
  beforeUnmount() {
    this.resultTimer && clearInterval(this.resultTimer);
    this.replyTimer && clearInterval(this.replyTimer);
  },
  methods: {
    getResultList(update) {
      if (!update) this.result.loading = true;
      if (!this.teamHave) {
        this.resultList = [];
        this.result.total = 0;
        this.result.loading = false;
        return;
      }
      API.competition_model.getCompetitionResultList({ cid: this.competitionId, pageNum: this.result.pageNum, pageSize: this.result.pageSize }).then((res) => {
        if (res.state === 'OK' && res.body) {
          for (let i = 0; i < res.body.list.length; i++) {
            let obj = res.body.list[i].scoreJson;
            for (const key in obj) {
              res.body.list[i][key] = obj[key];
            }
          }
          this.resultList = res.body.list || [];
          if (res.body.list.length == 0) {
            this.result.loading = false;
          } else if (this.resultColumns.length == 0) {
            // 提交记录指标名
            API.competition_model.getCompetitionGetScoreName({ cid: this.competitionId }).then((scoreNameRes) => {
              if (scoreNameRes.state === 'OK' && scoreNameRes.body != null) {
                let obj = scoreNameRes.body;
                this.resultTitleLength = Object.keys(obj).length;
                this.resultColumns = [
                  { title: '提交人', dataIndex: 'operatorname', key: 'operatorname', fixed: this.isRankinTitleBeyondFour ? 'left' : '', slots: { customRender: 'operatorname' }, width: 150, ellipsis: true },
                  { title: '提交状态', dataIndex: 'submitstatus', key: 'submitstatus', fixed: this.isRankinTitleBeyondFour ? 'right' : '', slots: { customRender: 'submitstatus' }, width: 150, ellipsis: true },
                  { title: '提交时间', dataIndex: 'createtime', key: 'createtime', fixed: this.isRankinTitleBeyondFour ? 'right' : '', width: 170, ellipsis: true },
                ];
                let targetColumn = [];
                for (let i in obj) {
                  targetColumn.push({ title: i, dataIndex: obj[i], key: obj[i], width: 150, ellipsis: true });
                }
                for (let i = targetColumn.length - 1; i >= 0; i--) {
                  this.resultColumns.splice(1, 0, targetColumn[i]);
                }
                if (this.updateBegin) this.updateList();
                this.resultList = res.body.list;
                this.result.total = res.body.total;
                this.result.loading = false;
              }
            });
          } else {
            this.result.loading = false;
          }
        }
      });
    },
    resultListChange(page) {
      this.result.pageNum = page;
      this.getResultList();
    },
    resultPageSizeChange(current) {
      this.result.pageSize = current;
      this.result.pageNum = 1;
      this.getResultList();
    },
    getReplyList(update) {
      if (!update) this.reply.loading = true;
      if (!this.teamHave || !this.showButton) {
        this.reply.list = [];
        this.reply.total = 0;
        this.reply.loading = false;
        return;
      }
      this.getReplyDownLoadType();
      const reqData = { cid: this.competitionId, pageNum: this.reply.pageNum, pageSize: this.reply.pageSize };
      API.competition_model.getCompetitionReplyList(reqData).then((res) => {
        if (res.state === 'OK' && res.body) {
          this.reply.list = res.body.list || [];
          if (this.reply.list.length > 0) {
            this.replyColumns = [
              { title: '提交人', dataIndex: 'operatorname', key: 'operatorname' },
              { title: '提交目录/文件名称', dataIndex: 'srcfilepath', key: 'srcfilepath' },
              { title: '提交状态', dataIndex: 'submitstatus', key: 'submitstatus', slots: { customRender: 'submitstatus' } },
              { title: '提交时间', dataIndex: 'createtime', key: 'createtime' },
              { title: '操作', key: 'operation', slots: { customRender: 'operation' } },
            ];
          }
          this.reply.total = res.body.total;
          this.reply.loading = false;
        }
      });
    },
    replyListChange(page, pageSize) {
      this.reply.pageNum = page;
      this.reply.pageSize = pageSize;
      this.getReplyList();
    },
    replyPageSizeChange(current) {
      this.reply.pageSize = current;
      this.reply.pageNum = 1;
      this.getReplyList();
    },
    resultFileStatus(i) {
      const status = {
        1: '成功',
        2: '失败',
        3: '评分中',
        4: '提交中',
        5: '评分中',
      };
      return status[i];
    },
    replyMaterialStatus(i) {
      const status = {
        1: '成功',
        2: '失败',
        3: '提交中',
        4: '提交中',
      };
      return status[i];
    },
    // 答辩材料记录是否允许下载
    getReplyDownLoadType() {
      API.competition_model.getReplyDownLoadType({ cid: this.competitionId }).then((res) => {
        if (res.state === 'OK') {
          this.replyAllowDownLoad = res.body;
        }
      });
    },
    // 答辩材料下载
    getCptReplyGetUrl(id, submitstatus) {
      if (submitstatus != 1 || !this.replyAllowDownLoad) return;
      API.competition_model.getCompetitionReplyGetUrl({ id }).then((res) => {
        if (res.state === 'OK' && res.body) {
          window.location = res.body.downloadUrl;
        }
      });
    },
    updateList() {
      this.goUpdate('result', this.getResultList);
      this.goUpdate('reply', this.getReplyList);
    },
    goUpdate(timerName, getList) {
      // typeId为3或5(正式赛-创意赛)每2秒调一次接口，否则每10秒调一次接口
      const timeInterval = OFFICIAL_INNOVATE_COMPETITION.includes(this.typeId) ? 2 * 1000 : 10 * 1000;
      const intervalId = setInterval(() => {
        const list = timerName === 'result' ? this.resultList : this.reply.list;
        const stopUpdateFlag = list.every((item) => item.submitstatus == RESULT_FILES_STATE.SUCCESS || item.submitstatus == RESULT_FILES_STATE.FAIL);

        if (stopUpdateFlag || list.length == 0) {
          clearInterval(timerName === 'result' ? this.resultTimer : this.replyTimer);
          timerName === 'result' ? (this.resultTimer = null) : (this.replyTimer = null);
        }
        getList('update');
      }, timeInterval);

      timerName === 'result' ? (this.resultTimer = intervalId) : (this.replyTimer = intervalId);
    },
  },
};
</script>
<style lang="less" scoped>
.submit-record {
  margin-top: 36px;
}
.competition-table {
  width: 1136px;
  margin: 24px 0 16px 0;
  .click-disable {
    color: #cbcfd2;
    cursor: no-drop;
  }
  .state-common {
    font-size: 14px;
    font-weight: 400;
  }
  // 提交状态：1评分成功，2评分失败，3评分中，4提交中,5待评分
  .succeed-state {
    .state-common();
    color: #17bb85;
  }
  .fail-state {
    .state-common();
    color: #ff454d;
  }
  .submit-state {
    .state-common();
    color: #ff9d00;
  }
}
a {
  color: #0082ffff;
}
</style>
