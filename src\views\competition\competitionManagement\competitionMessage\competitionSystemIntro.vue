<template>
  <div class="competition-system-intro">
    <jt-common-content :loading="loading" :empty="systemIntro.length === 0" :style="{ 'min-height': '416px' }" empty-title="您暂未设置赛制介绍">
      <template #empty-operation>
        <p v-if="showEditBtn" class="to-edit-page" @click="toEditPage">编辑赛制介绍</p>
      </template>
      <html-container v-for="(item, index) in systemIntro" :key="item.title" :style="{ 'margin-top': index > 0 ? '40px' : '0px' }" :text-value="item.value" :title="item.title" :text-url="item.content" @updateRichLoading="updateRichLoading" />
    </jt-common-content>
  </div>
</template>

<script>
import htmlContainer from '../components/htmlContainer.vue';
import { competitionApi } from '@/apis/index';
export default {
  name: 'CompetitionSystemIntro',
  components: {
    htmlContainer,
  },
  props: {
    dataList: {
      type: Array,
      default: () => [],
    },
    showEditBtn: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['toEdit'],
  data() {
    return {
      systemIntro: [],
      loading: false,
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      this.loading = true;
      if (this.dataList) {
        this.systemIntro = this.dataList.map((item) => {
          return {
            title: item.inputValue,
            value: item.editorValue,
          };
        });
        this.loading = false;
      } else {
        this.initSystemIntro();
      }
    },
    async initSystemIntro() {
      const cid = this.$route.params.competitionId;
      const res = await competitionApi.getCompetitionDesAndIntro({ cid });
      if (res.state === 'OK') {
        this.systemIntro = res.body.descriptionPathJson || [];
      }
      this.loading = false;
    },
    toEditPage() {
      this.$emit('toEdit');
    },
    updateRichLoading(val) {
      this.loading = val;
    },
  },
};
</script>

<style lang="less" scoped>
.to-edit-page {
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  color: #0082ff;
}
</style>
